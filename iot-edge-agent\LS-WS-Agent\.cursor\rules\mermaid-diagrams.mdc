---
description: 
globs: 
alwaysApply: false
---
# Mermaid图表编写规范

本规则提供在Markdown文档中使用Mermaid图表的最佳实践，以确保图表能够正确渲染并避免常见语法错误。

## 基本原则

1. **简洁性**: 图表应尽量简单明了，避免过于复杂的结构。
2. **兼容性**: 使用较基础的Mermaid语法，确保在各种Markdown渲染环境中正常显示。，使用的是mermaid 10.9.1 及一下的版本
3. **可读性**: 图表设计应当直观易懂，标签文本清晰。

## 常见错误及解决方案

### 节点标签中的特殊字符处理

当节点标签包含特殊字符时，需要使用引号包裹：

- **错误写法**:
  ```
  nodeA[Label with (parentheses)]
  nodeB[Label with <br/> HTML tags]
  ```

- **正确写法**:
  ```
  nodeA["Label with (parentheses)"]
  nodeB["Label with line breaks"]
  ```

### 复杂图表分解

当需要表达复杂逻辑时，将一个大图表分解为多个小图表：

- **不推荐**: 单个复杂图表包含大量节点和关系
- **推荐**: 多个小图表，每个关注一个特定方面或层次

### 样式简化

保持样式简单，避免过度装饰：

- **过度样式**:
  ```
  classDef complex fill:#f9f,stroke:#333,stroke-width:4px,stroke-dasharray: 5 5,color:red,font-size:20px;
  ```

- **简化样式**:
  ```
  classDef simple fill:#f9f,stroke:#333,stroke-width:2px;
  ```

## 实际案例参考

以下是一些正确编写的Mermaid图表示例，可供参考：

### 流程图示例

```mermaid
graph TD
    A[开始] --> B["处理步骤1"]
    B --> C["处理步骤2"]
    C --> D{判断条件}
    D -- 是 --> E[结果1]
    D -- 否 --> F[结果2]
    E --> G[结束]
    F --> G
```

### 时序图示例

```mermaid
sequenceDiagram
    参与者A->>参与者B: 请求数据
    参与者B->>参与者C: 转发请求
    参与者C-->>参与者B: 返回数据
    参与者B-->>参与者A: 响应
```

## 总结

- 始终用引号包裹包含特殊字符的节点标签
- 避免在节点标签中使用HTML标签
- 将复杂图表分解为多个简单图表
- 保持样式简约
- 优先使用基础语法功能，避免使用实验性或高级特性
