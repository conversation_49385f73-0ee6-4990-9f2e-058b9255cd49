/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.ws.ls;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@NoArgsConstructor
public class ApiPageResponse<T> {

    private boolean success;
    private String message;
    private T data;
    private long length = 0; // 数据长度
    private long total = 0; // 总记录数
    private long currentPage = 0; // 当前页码

    public ApiPageResponse(boolean success, String message, T data, long length, long totalElements, long currentPage) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.length = length;
        this.total = totalElements;
        this.currentPage = currentPage;
    }
}