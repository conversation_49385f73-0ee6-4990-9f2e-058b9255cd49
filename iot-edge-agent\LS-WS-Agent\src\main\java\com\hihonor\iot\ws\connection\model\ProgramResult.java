package com.hihonor.iot.ws.connection.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PROGRAM_TASK_RESULT 消息中 programs 数组内每个程序执行结果的结构。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgramResult {

    /**
     * 轨道号。
     * (String, Mandatory)
     */
    private String trackId;

    /**
     * 程序名称。
     * (String, Mandatory)
     */
    private String programName;

    /**
     * 操作是否成功。
     * (Boolean, Mandatory, true 表示成功, false 表示失败)
     */
    private boolean success;

    /**
     * 执行结果的详细描述信息。对于失败情况，应包含错误原因。
     * (String, Optional)
     */
    private String resultMessage;
}