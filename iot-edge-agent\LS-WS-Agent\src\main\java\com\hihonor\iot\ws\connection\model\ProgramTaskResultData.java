package com.hihonor.iot.ws.connection.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PROGRAM_TASK_RESULT 消息的 data 字段结构。
 * 设备向 IOT 平台上报程序与任务的执行状态/结果。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgramTaskResultData {

    /**
     * 来源设备的 resourceid。
     * (String, Mandatory)
     */
    private String resourceid;

    /**
     * 对应的生产任务令。
     * (String, Mandatory)
     */
    private String taskOrder;

    /**
     * 对应程序的执行结果列表。
     * (List of ProgramResult, Mandatory)
     */
    private List<ProgramResult> programs;
}