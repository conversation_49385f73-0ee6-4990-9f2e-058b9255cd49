/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.ws.ls.listener;

import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.sdk.listener.IIoTClientStatusListener;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@NoArgsConstructor
@Component
@Slf4j
public class IotClientStatusListener implements IIoTClientStatusListener {
//
//    @Autowired
//    ThingManagement thingManagement;
//
//
//    @Autowired
//    ApplicationContext context;


    void test() {

    }

    /**
     * sss
     */
    @Override
    public void onClientConnected() {
        try {
       //     thingManagement.initThing();
         //   test();
        } catch (Exception e) {
            log.error("onClientConnected error", e);
        }

    }

    /**
     * ss
     */
    @Override
    public void onClientReConnected() {
        log.info("onClientReConnected");
    }

    /**
     * ss
     */
    @Override
    public void onClientDisConnected() {
        log.info("onClientDisConnected");
    }
}
