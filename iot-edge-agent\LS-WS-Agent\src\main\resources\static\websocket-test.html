<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备 WebSocket 测试 (新)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px; /* 调整宽度以便显示 resourceid */
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: calc(100% - 150px); /* 调整宽度 */
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .disconnect {
            background-color: #f44336;
        }
        .disconnect:hover {
            background-color: #da190b;
        }
        .log {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>设备 WebSocket 测试工具 (新版)</h1>
    
    <div class="container">
        <h3>连接配置</h3>
        <div class="input-group">
            <label>Resource ID:</label> <!-- deviceId -> resourceid -->
            <input type="text" id="resourceid" value="deviceTest001" placeholder="输入 Resource ID">
        </div>
        <div class="input-group">
            <label>服务器 WS 地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080" placeholder="WebSocket服务器地址 (e.g., ws://host:port)">
        </div>
        <div class="input-group">
            <label>自定义请求头:</label>
            <input type="text" id="customHeaderKey" value="X-resourceid" placeholder="请求头 Key (例如 X-resourceid)">
            <input type="text" id="customHeaderValue" style="width: calc(100% - 280px); margin-left: 5px;" placeholder="请求头 Value (使用查询参数时可留空)">
        </div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled class="disconnect">断开</button>
    </div>

    <div class="status disconnected" id="status">未连接</div>

    <div class="container">
        <h3>消息发送</h3>
        <div class="input-group">
            <label>消息内容 (JSON):</label>
            <textarea id="messageInput" placeholder="输入要发送的JSON消息"></textarea>
        </div>
        <button onclick="sendMessage()" id="sendBtn" disabled>发送自定义消息</button>
        <button onclick="sendHeartbeat()" id="heartbeatBtn" disabled>发送心跳</button>
        <button onclick="sendSampleData()" id="dataBtn" disabled>发送示例数据</button>
    </div>

    <div class="container">
        <h3>消息日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div class="log" id="log"></div>
    </div>

    <script>
        let websocket = null;
        let connected = false;

        function logMessage(message) { // Renamed log to logMessage to avoid conflict with console.log
            const now = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${now}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(isConnected) {
            connected = isConnected;
            const statusElement = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            const heartbeatBtn = document.getElementById('heartbeatBtn');
            const dataBtn = document.getElementById('dataBtn');

            if (isConnected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                heartbeatBtn.disabled = false;
                dataBtn.disabled = false;
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                heartbeatBtn.disabled = true;
                dataBtn.disabled = true;
            }
        }

        function connect() {
            const resourceid_val = document.getElementById('resourceid').value; // deviceId -> resourceid
            const serverUrl_val = document.getElementById('serverUrl').value;
            const customHeaderKey_val = document.getElementById('customHeaderKey').value;
            const customHeaderValue_val = document.getElementById('customHeaderValue').value;

            if (!resourceid_val || !serverUrl_val) {
                alert('请填写 Resource ID 和服务器 WS 地址');
                return;
            }

            // 构建 WebSocket URL，优先使用查询参数传递 resourceid
            // 服务端 DeviceWebSocketServer 的 @WebSocketEndpoint path 为 "/ws/connect"
            let wsUrl = `${serverUrl_val}/ws/connect?resourceid=${encodeURIComponent(resourceid_val)}`;
            logMessage(`正在连接到: ${wsUrl}`);
            
            // 浏览器 WebSocket API 不支持直接设置自定义请求头。
            // 如果需要通过请求头传递 resourceid，客户端需要使用支持自定义头的库，或者服务器端配置允许从其他标准头读取。
            // 这里我们主要依赖查询参数。
            // 如果 customHeaderKey_val 和 customHeaderValue_val 有值，可以提示用户这在浏览器中不会生效。
            if (customHeaderKey_val && customHeaderValue_val) {
                logMessage(`提示: 浏览器 WebSocket API 不支持直接设置自定义请求头 ('${customHeaderKey_val}'). resourceid 将通过查询参数传递。`);
            }

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    logMessage('WebSocket 连接已建立');
                    updateStatus(true);
                };

                websocket.onmessage = function(event) {
                    logMessage(`收到消息: ${event.data}`);
                };

                websocket.onclose = function(event) {
                    logMessage(`WebSocket 连接已关闭, Code: ${event.code}, Reason: ${event.reason || 'N/A'}, WasClean: ${event.wasClean}`);
                    updateStatus(false);
                };

                websocket.onerror = function(error) {
                    // 对于连接错误，onerror通常先于onclose触发，error对象可能不包含太多信息
                    logMessage(`WebSocket 连接错误。请检查控制台获取更多详情。`);
                    console.error('WebSocket Error:', error);
                    updateStatus(false);
                };

            } catch (error) {
                logMessage(`连接尝试失败: ${error.message}`);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (websocket && connected) {
                websocket.close(1000, "用户主动断开"); // code 1000 for normal closure
                logMessage('主动断开连接');
            }
        }

        function sendMessage() {
            const message = document.getElementById('messageInput').value;
            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (websocket && connected) {
                websocket.send(message);
                logMessage(`发送消息: ${message}`);
                // document.getElementById('messageInput').value = ''; // 可选：发送后清空输入框
            } else {
                alert('WebSocket 未连接');
            }
        }

        function sendHeartbeat() {
            if (websocket && connected) {
                const heartbeatMsg = JSON.stringify({ type: "heartbeat", timestamp: Date.now() });
                websocket.send(heartbeatMsg);
                logMessage(`发送心跳: ${heartbeatMsg}`);
            } else {
                alert('WebSocket 未连接');
            }
        }

        function sendSampleData() {
            const sampleData = {
                type: 'sampleDeviceData',
                payload: {
                    temperature: (Math.random() * 10 + 20).toFixed(1),
                    voltage: (Math.random() * 0.5 + 11.8).toFixed(2),
                },
                timestamp: Date.now()
            };

            if (websocket && connected) {
                const message = JSON.stringify(sampleData);
                websocket.send(message);
                logMessage(`发送示例数据: ${message}`);
            } else {
                alert('WebSocket 未连接');
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        window.onload = function() {
            updateStatus(false);
            logMessage('页面准备就绪。请配置连接信息并点击连接。');
        };

        window.onbeforeunload = function() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.close(1001, "页面关闭"); // 1001: Going Away
            }
        };
    </script>
</body>
</html> 