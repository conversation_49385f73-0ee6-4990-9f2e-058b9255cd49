package com.hihonor.iot.ws.connection.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * HEARTBEAT 消息的 data 字段结构。
 * data 字段本身对于HEARTBEAT消息是可选的，可以是一个空对象，
 * 或者包含设备状态的摘要信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // 序列化时忽略null字段，如可选的 status
public class HeartbeatData {

    /**
     * 设备当前状态简述。
     * (String, Optional, e.g., "IDLE", "RUNNING")
     */
    private String status;
}