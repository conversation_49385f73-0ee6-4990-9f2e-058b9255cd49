# WebSocket 协议与 Netty 框架实战教程

## 目录

1. [WebSocket 协议基础](#websocket-协议基础)
2. [WebSocket 协议原理深入](#websocket-协议原理深入)
3. [netty-websocket-spring-boot-starter 框架详解](#框架详解)
4. [WebSocket 连接生命周期详解](#websocket-连接生命周期详解)
5. [实战代码示例](#实战代码示例)
6. [应用场景与最佳实践](#应用场景与最佳实践)

---

## WebSocket 协议基础

### 什么是 WebSocket

WebSocket 是一种在单个 TCP 连接上进行全双工通信的协议。它于 2011 年被 IETF 定为标准 RFC 6455，并由 HTML5 规范引入浏览器。

#### 传统 HTTP vs WebSocket

```mermaid
graph TB
    subgraph "HTTP 轮询模式"
        A1[客户端] -->|1. 请求| B1[服务器]
        B1 -->|2. 响应| A1
        A1 -->|3. 再次请求| B1
        B1 -->|4. 响应| A1
        A1 -->|5. 循环请求| B1
    end

    subgraph "WebSocket 模式"
        A2[客户端] -->|1. 握手请求| B2[服务器]
        B2 -->|2. 握手响应| A2
        A2 <-->|3. 双向通信| B2
    end

    classDef http fill:#ffecb3
    classDef ws fill:#e1f5fe

    class A1,B1 http
    class A2,B2 ws
```

#### WebSocket 的优势

1. **低延迟**：无需 HTTP 请求/响应开销
2. **全双工**：客户端和服务器可同时发送数据
3. **低开销**：建立连接后，数据帧开销很小
4. **实时性**：服务器可主动推送数据
5. **跨域支持**：通过 CORS 机制支持跨域

### WebSocket 握手过程

#### 握手请求（客户端发送）

```http
GET /chat HTTP/1.1
Host: server.example.com
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
Sec-WebSocket-Protocol: chat, superchat
Sec-WebSocket-Version: 13
Origin: http://example.com
```

#### 握手响应（服务器返回）

```http
HTTP/1.1 101 Switching Protocols
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Accept: s3pPLMBiTxaQ9kYGzzhZRbK+xOo=
Sec-WebSocket-Protocol: chat
```

#### 关键字段说明

| 字段                     | 说明                 |
| ------------------------ | -------------------- |
| `Upgrade: websocket`     | 协议升级到 WebSocket |
| `Connection: Upgrade`    | 连接升级             |
| `Sec-WebSocket-Key`      | 客户端生成的随机密钥 |
| `Sec-WebSocket-Accept`   | 服务器计算的确认密钥 |
| `Sec-WebSocket-Version`  | WebSocket 协议版本   |
| `Sec-WebSocket-Protocol` | 子协议协商           |

---

## WebSocket 协议原理深入

### 数据帧结构

WebSocket 数据以帧的形式传输，每个帧都有特定的结构：

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-------+-+-------------+-------------------------------+
|F|R|R|R| opcode|M| Payload len |    Extended payload length    |
|I|S|S|S|  (4)  |A|     (7)     |             (16/64)           |
|N|V|V|V|       |S|             |   (if payload len==126/127)   |
| |1|2|3|       |K|             |                               |
+-+-+-+-+-------+-+-------------+ - - - - - - - - - - - - - - - +
|     Extended payload length continued, if payload len == 127  |
+ - - - - - - - - - - - - - - - +-------------------------------+
|                               |Masking-key, if MASK set to 1  |
+-------------------------------+-------------------------------+
| Masking-key (continued)       |          Payload Data         |
+-------------------------------- - - - - - - - - - - - - - - - +
:                     Payload Data continued ...                :
+ - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - +
|                     Payload Data continued ...                |
+---------------------------------------------------------------+
```

#### 帧类型（Opcode）

| Opcode | 说明     | 用途             |
| ------ | -------- | ---------------- |
| 0x0    | 继续帧   | 分片消息的中间帧 |
| 0x1    | 文本帧   | UTF-8 文本数据   |
| 0x2    | 二进制帧 | 二进制数据       |
| 0x8    | 关闭帧   | 关闭连接         |
| 0x9    | Ping 帧  | 心跳检测         |
| 0xA    | Pong 帧  | 心跳响应         |

### 消息分片机制

大消息可以分片传输，提高传输效率：

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器

    Note over C,S: 发送大文件（分片传输）

    C->>S: 第一帧 (FIN=0, opcode=0x2)
    C->>S: 中间帧 (FIN=0, opcode=0x0)
    C->>S: 中间帧 (FIN=0, opcode=0x0)
    C->>S: 最后帧 (FIN=1, opcode=0x0)

    S->>C: 确认接收完整消息
```

### 连接状态管理

```mermaid
stateDiagram-v2
    [*] --> CONNECTING
    CONNECTING --> OPEN: 握手成功
    CONNECTING --> CLOSED: 握手失败
    OPEN --> CLOSING: 发送关闭帧
    CLOSING --> CLOSED: 接收关闭帧
    OPEN --> CLOSED: 连接异常
    CLOSED --> [*]
```

---

## 框架详解

### netty-websocket-spring-boot-starter 架构

```mermaid
graph TB
    subgraph "Spring Boot 应用层"
        A[Controller层]
        B[Service层]
        C[WebSocket 端点]
    end

    subgraph "框架抽象层"
        D[注解处理器]
        E[端点注册器]
        F[会话管理器]
        G[消息路由器]
    end

    subgraph "Netty 网络层"
        H[ServerBootstrap]
        I[ChannelPipeline]
        J[WebSocket 编解码器]
        K[自定义Handler]
    end

    subgraph "底层传输层"
        L[NioServerSocketChannel]
        M[EventLoopGroup]
        N[ByteBuf]
    end

    A --> D
    B --> E
    C --> F

    D --> H
    E --> I
    F --> J
    G --> K

    H --> L
    I --> M
    J --> N

    classDef app fill:#e3f2fd
    classDef framework fill:#f3e5f5
    classDef netty fill:#fff3e0
    classDef transport fill:#e8f5e8

    class A,B,C app
    class D,E,F,G framework
    class H,I,J,K netty
    class L,M,N transport
```

### 核心组件详解

#### 1. WebSocketEndpoint 注解

```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface WebSocketEndpoint {
    /**
     * 端点路径，支持路径参数
     * 例如："/chat/{roomId}"
     */
    String path() default "/";

    /**
     * 绑定主机，默认 0.0.0.0
     */
    String host() default "0.0.0.0";

    /**
     * 端口号，默认从配置文件读取
     */
    int port() default 0;

    /**
     * 子协议列表
     */
    String[] subprotocols() default {};
}
```

#### 2. 会话管理（NettySession）

```java
public interface NettySession {
    /**
     * 获取会话ID
     */
    String getId();

    /**
     * 发送文本消息
     */
    ChannelFuture sendText(String text);

    /**
     * 发送二进制消息
     */
    ChannelFuture sendBinary(byte[] bytes);

    /**
     * 关闭连接
     */
    void close();

    /**
     * 是否已连接
     */
    boolean isOpen();

    /**
     * 设置属性
     */
    void setAttribute(String key, Object value);

    /**
     * 获取属性
     */
    <T> T getAttribute(String key);

    /**
     * 获取远程地址
     */
    SocketAddress getRemoteAddress();
}
```

#### 3. 参数注入机制

框架支持多种参数注入：

```java
@WebSocketEndpoint(path = "/api/{module}/{action}")
public class AdvancedEndpoint {

    @OnOpen
    public void onOpen(
        NettySession session,                    // 会话对象
        HttpHeaders headers,                     // HTTP 头
        @PathVariable String module,             // 路径参数
        @PathVariable String action,             // 路径参数
        @RequestParam String token,              // 查询参数
        @RequestParam Map<String, String> params // 所有查询参数
    ) {
        // 处理连接
    }
}
```

---

## WebSocket 连接生命周期详解

### 连接生命周期概述

WebSocket 连接从建立到关闭经历多个阶段，netty-websocket-spring-boot-starter 框架通过注解方式让开发者能够在每个关键节点介入处理业务逻辑。

#### 完整生命周期流程图

```mermaid
stateDiagram-v2
    [*] --> 握手前准备: 客户端发起连接
    握手前准备 --> @BeforeHandshake: 验证连接参数

    @BeforeHandshake --> 握手失败: 验证失败
    握手失败 --> [*]: 连接关闭

    @BeforeHandshake --> 握手成功: 验证通过
    握手成功 --> @OnOpen: 连接建立

    @OnOpen --> 活跃通信: 连接就绪
    活跃通信 --> @OnMessage: 接收消息
    活跃通信 --> @OnBinary: 接收二进制数据
    活跃通信 --> @OnEvent: 事件触发

    @OnMessage --> 活跃通信: 继续通信
    @OnBinary --> 活跃通信: 继续通信
    @OnEvent --> 活跃通信: 继续通信

    活跃通信 --> 连接异常: 网络异常/超时
    连接异常 --> @OnError: 错误处理
    @OnError --> @OnClose: 清理资源

    活跃通信 --> 主动关闭: 客户端/服务端关闭
    主动关闭 --> @OnClose: 正常关闭

    @OnClose --> [*]: 连接结束

    classDef handshake fill:#fff2cc
    classDef active fill:#d4edda
    classDef error fill:#f8d7da
    classDef close fill:#d1ecf1

    class 握手前准备,@BeforeHandshake,握手成功 handshake
    class 活跃通信,@OnMessage,@OnBinary,@OnEvent active
    class 连接异常,@OnError error
    class 主动关闭,@OnClose close
```

### 详细时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant F as 框架层
    participant B as 业务层
    participant S as 会话管理

    Note over C,S: 1. 连接建立阶段

    C->>F: WebSocket 握手请求
    F->>B: @BeforeHandshake

    alt 验证通过
        B->>F: 继续处理
        F->>C: 握手成功响应
        F->>S: 创建NettySession
        F->>B: @OnOpen
        B->>S: 初始化用户状态

        Note over C,S: 2. 活跃通信阶段

        loop 消息交互
            C->>F: 发送文本消息
            F->>B: @OnMessage
            B->>S: 处理业务逻辑
            B->>F: 发送响应
            F->>C: 推送消息
        end

        loop 二进制数据
            C->>F: 发送二进制数据
            F->>B: @OnBinary
            B->>S: 处理文件/图片等
        end

        loop 事件处理
            F->>B: @OnEvent(心跳超时等)
            B->>S: 处理特殊事件
        end

        Note over C,S: 3. 连接关闭阶段

        alt 正常关闭
            C->>F: 关闭连接
            F->>B: @OnClose
            B->>S: 清理用户状态
        else 异常关闭
            F->>B: @OnError
            B->>S: 记录错误日志
            F->>B: @OnClose
            B->>S: 清理用户状态
        end

    else 验证失败
        B->>F: 拒绝连接
        F->>C: 握手失败
    end
```

### 各阶段详细解析

#### 1. @BeforeHandshake - 握手前验证

**触发时机**：WebSocket 握手请求到达但尚未建立连接时

**主要用途**：

- 用户身份验证
- 权限检查
- 参数验证
- 连接限制

**聊天室案例分析**：

```java
@BeforeHandshake
public void beforeHandshake(NettySession session, HttpHeaders headers,
                           @PathVariable String roomId,
                           @RequestParam String username) {
    // 1. 验证用户名格式
    if (StringUtils.isEmpty(username) || username.length() > 20) {
        log.warn("用户名验证失败: username={}", username);
        session.close(); // 拒绝连接
        return;
    }

    // 2. 检查房间是否存在
    if (!roomService.isRoomExists(roomId)) {
        log.warn("房间不存在: roomId={}", roomId);
        session.close();
        return;
    }

    // 3. 检查房间人数限制
    if (getRoomUserCount(roomId) >= MAX_ROOM_SIZE) {
        log.warn("房间人数已满: roomId={}", roomId);
        session.close();
        return;
    }

    // 4. 验证通过，保存用户信息到会话
    session.setAttribute("roomId", roomId);
    session.setAttribute("username", username);
    session.setAttribute("joinTime", System.currentTimeMillis());

    log.info("用户准备加入房间: username={}, roomId={}", username, roomId);
}
```

**IoT 设备案例分析**：

```java
@BeforeHandshake
public void beforeHandshake(NettySession session, HttpHeaders headers,
                           @PathVariable String deviceId) {
    // 1. 设备认证
    String token = headers.get("Authorization");
    if (!deviceManager.validateDevice(deviceId, token)) {
        log.warn("设备认证失败: deviceId={}, token={}", deviceId, token);
        session.close();
        return;
    }

    // 2. 检查设备状态
    DeviceStatus status = deviceManager.getDeviceStatus(deviceId);
    if (status == DeviceStatus.DISABLED) {
        log.warn("设备已被禁用: deviceId={}", deviceId);
        session.close();
        return;
    }

    // 3. 检查并发连接数
    if (deviceManager.getActiveConnectionCount(deviceId) > 0) {
        log.warn("设备重复连接: deviceId={}", deviceId);
        // 可以选择踢掉旧连接或拒绝新连接
        deviceManager.closeExistingConnections(deviceId);
    }

    // 4. 保存设备信息
    session.setAttribute("deviceId", deviceId);
    session.setAttribute("authTime", System.currentTimeMillis());
    session.setAttribute("deviceType", deviceManager.getDeviceType(deviceId));
}
```

#### 2. @OnOpen - 连接建立成功

**触发时机**：WebSocket 握手成功，连接正式建立后

**主要用途**：

- 初始化连接状态
- 注册会话到管理器
- 发送欢迎消息
- 启动定时任务

**连接建立详细流程图**：

```mermaid
flowchart TB
    A[握手成功] --> B["@OnOpen触发"]
    B --> C[注册会话到管理器]
    C --> D["初始化用户/设备状态"]
    D --> E[发送初始化数据]
    E --> F[启动心跳检测]
    F --> G[广播连接事件]
    G --> H[连接就绪]

    subgraph "聊天室场景"
        C --> C1[加入房间会话列表]
        D --> D1[设置用户在线状态]
        E --> E1[发送房间用户列表]
        G --> G1[广播用户加入消息]
    end

    subgraph "IoT 设备场景"
        C --> C2[注册设备连接]
        D --> D2[更新设备在线状态]
        E --> E2[下发设备配置]
        F --> F2[启动心跳监控]
    end

    classDef process fill:#e1f5fe
    classDef chat fill:#fff3e0
    classDef iot fill:#f3e5f5

    class A,B,C,D,E,F,G,H process
    class C1,D1,E1,G1 chat
    class C2,D2,E2,F2 iot
```

**聊天室案例详细分析**：

```java
@OnOpen
public void onOpen(NettySession session, @PathVariable String roomId) {
    String username = session.getAttribute("username");
    long joinTime = session.getAttribute("joinTime");

    // 1. 加入房间会话管理
    Set<NettySession> roomSessions = ROOMS.computeIfAbsent(roomId,
        k -> ConcurrentHashMap.newKeySet());
    roomSessions.add(session);

    // 2. 更新用户在线状态
    userService.setUserOnline(username, roomId);

    // 3. 发送房间初始化信息
    RoomInfo roomInfo = buildRoomInfo(roomId);
    session.sendText(JSON.toJSONString(roomInfo));

    // 4. 广播用户加入消息
    ChatMessage joinMessage = ChatMessage.builder()
        .type("system")
        .content(username + " 加入了聊天室")
        .timestamp(System.currentTimeMillis())
        .roomId(roomId)
        .build();
    broadcastToRoom(roomId, joinMessage);

    // 5. 发送历史消息（最近50条）
    List<ChatMessage> history = chatHistoryService.getRecentMessages(roomId, 50);
    session.sendText(JSON.toJSONString(Map.of("type", "history", "messages", history)));

    // 6. 启动用户活跃度检测
    startUserActivityMonitor(session, username);

    log.info("用户成功加入房间: username={}, roomId={}, 当前人数={}",
            username, roomId, roomSessions.size());
}
```

#### 3. @OnMessage - 消息处理

**触发时机**：接收到客户端发送的文本消息时

**主要用途**：

- 处理业务消息
- 消息路由转发
- 协议解析
- 业务逻辑处理

**消息处理流程图**：

```mermaid
flowchart TB
    A[接收消息] --> B[消息格式验证]
    B --> C{验证通过?}
    C -->|否| D[返回错误信息]
    C -->|是| E[解析消息类型]

    E --> F{消息类型}
    F -->|chat| G[聊天消息处理]
    F -->|heartbeat| H[心跳处理]
    F -->|command| I[命令处理]
    F -->|file| J[文件消息处理]

    G --> K[内容过滤]
    K --> L[广播给房间用户]
    L --> M[保存聊天记录]

    H --> N[更新心跳时间]
    N --> O[返回心跳响应]

    I --> P[权限验证]
    P --> Q[执行命令]
    Q --> R[返回执行结果]

    J --> S[文件类型检查]
    S --> T[上传文件处理]
    T --> U[生成分享链接]

    M --> V[消息处理完成]
    O --> V
    R --> V
    U --> V
    D --> V

    classDef input fill:#fff2cc
    classDef process fill:#e1f5fe
    classDef output fill:#d4edda
    classDef error fill:#f8d7da

    class A input
    class B,E,K,N,P,S process
    class L,M,O,Q,T,U,V output
    class C,D error
```

**IoT 设备消息处理案例**：

```java
@OnMessage
public void onMessage(NettySession session, String message) {
    String deviceId = session.getAttribute("deviceId");
    String deviceType = session.getAttribute("deviceType");

    try {
        // 1. 消息预处理
        DeviceMessage deviceMsg = parseDeviceMessage(message);
        deviceMsg.setDeviceId(deviceId);
        deviceMsg.setReceiveTime(System.currentTimeMillis());

        // 2. 消息类型路由处理
        switch (deviceMsg.getType()) {
            case "heartbeat":
                handleHeartbeat(deviceId, session, deviceMsg);
                break;
            case "sensor_data":
                handleSensorData(deviceId, deviceMsg);
                break;
            case "status_report":
                handleStatusReport(deviceId, deviceMsg);
                break;
            case "alarm":
                handleAlarmMessage(deviceId, deviceMsg);
                break;
            case "config_response":
                handleConfigResponse(deviceId, deviceMsg);
                break;
            default:
                log.warn("未知消息类型: deviceId={}, type={}", deviceId, deviceMsg.getType());
                sendErrorResponse(session, "UNKNOWN_MESSAGE_TYPE", "未知的消息类型");
        }

        // 3. 更新设备活跃时间
        deviceManager.updateLastActiveTime(deviceId);

    } catch (Exception e) {
        log.error("设备消息处理失败: deviceId={}, message={}", deviceId, message, e);
        sendErrorResponse(session, "MESSAGE_PROCESS_ERROR", "消息处理失败: " + e.getMessage());
    }
}

// 传感器数据处理详细流程
private void handleSensorData(String deviceId, DeviceMessage message) {
    SensorData sensorData = message.getData();

    // 1. 数据验证
    if (!dataValidator.validate(deviceId, sensorData)) {
        log.warn("传感器数据验证失败: deviceId={}, data={}", deviceId, sensorData);
        return;
    }

    // 2. 数据存储
    CompletableFuture.runAsync(() -> {
        dataStorageService.store(deviceId, sensorData);
    });

    // 3. 实时数据分析
    AnalysisResult result = dataAnalyzer.analyze(deviceId, sensorData);

    // 4. 异常检测和告警
    if (result.hasAnomaly()) {
        alarmService.triggerAlarm(deviceId, result.getAnomalyType(), sensorData);
    }

    // 5. 实时数据推送给监控客户端
    monitoringService.broadcastSensorData(deviceId, sensorData);
}
```

#### 4. @OnBinary - 二进制数据处理

**触发时机**：接收到客户端发送的二进制数据时

**主要用途**：

- 文件传输
- 图片/视频处理
- 固件升级
- 大数据传输

#### 5. @OnEvent - 事件处理

**触发时机**：特定事件发生时（如心跳超时、读写超时等）

**事件类型详解图**：

```mermaid
mindmap
  root[@OnEvent 事件类型]
    空闲事件
      读空闲
        客户端长时间未发送数据
        触发心跳检测
      写空闲
        服务端长时间未发送数据
        主动发送心跳
      读写空闲
        双向都无数据传输
        考虑关闭连接

    连接事件
      连接激活
        连接刚建立
        初始化操作
      连接失活
        连接即将关闭
        清理操作

    自定义事件
      业务事件
        用户操作事件
        系统状态变更
      定时事件
        定时任务触发
        周期性检查
```

#### 6. @OnError - 错误处理

**触发时机**：连接过程中发生异常时

**常见错误类型和处理策略**：

```mermaid
flowchart TB
    A[异常发生] --> B{异常类型判断}

    B -->|网络异常| C[IOException]
    B -->|协议异常| D[ProtocolException]
    B -->|业务异常| E[BusinessException]
    B -->|其他异常| F[RuntimeException]

    C --> C1[记录网络状态]
    C1 --> C2[尝试重连机制]
    C2 --> G[通知监控系统]

    D --> D1[记录协议错误]
    D1 --> D2[发送错误响应]
    D2 --> G

    E --> E1[记录业务异常]
    E1 --> E2[业务回滚]
    E2 --> G

    F --> F1[记录未知异常]
    F1 --> F2[紧急处理]
    F2 --> G

    G --> H[清理资源]
    H --> I[关闭连接]

    classDef error fill:#f8d7da
    classDef process fill:#e1f5fe
    classDef final fill:#d4edda

    class A,B,C,D,E,F error
    class C1,C2,D1,D2,E1,E2,F1,F2 process
    class G,H,I final
```

**错误处理实践案例**：

```java
@OnError
public void onError(NettySession session, Throwable throwable) {
    String deviceId = session.getAttribute("deviceId");
    String deviceType = session.getAttribute("deviceType");

    // 1. 错误分类处理
    if (throwable instanceof IOException) {
        // 网络异常
        log.warn("设备网络异常: deviceId={}, error={}", deviceId, throwable.getMessage());
        deviceManager.markDeviceOffline(deviceId, "NETWORK_ERROR");

    } else if (throwable instanceof ReadTimeoutException) {
        // 读超时
        log.warn("设备读取超时: deviceId={}", deviceId);
        deviceManager.markDeviceOffline(deviceId, "READ_TIMEOUT");

    } else if (throwable instanceof WriteTimeoutException) {
        // 写超时
        log.warn("设备写入超时: deviceId={}", deviceId);
        deviceManager.markDeviceOffline(deviceId, "WRITE_TIMEOUT");

    } else {
        // 其他异常
        log.error("设备连接异常: deviceId={}", deviceId, throwable);
        deviceManager.markDeviceOffline(deviceId, "UNKNOWN_ERROR");
    }

    // 2. 异常统计
    errorStatistics.recordError(deviceId, deviceType, throwable.getClass().getSimpleName());

    // 3. 告警通知
    if (isHighPriorityDevice(deviceId)) {
        alarmService.sendDeviceErrorAlarm(deviceId, throwable);
    }

    // 4. 自动恢复机制
    if (shouldAutoRecover(deviceId, throwable)) {
        scheduleAutoRecovery(deviceId);
    }
}
```

#### 7. @OnClose - 连接关闭

**触发时机**：WebSocket 连接关闭时（无论是正常关闭还是异常关闭）

**连接关闭流程图**：

```mermaid
sequenceDiagram
    participant C as 客户端
    participant F as 框架层
    participant B as 业务层
    participant S as 会话管理
    participant M as 监控系统

    Note over C,M: 连接关闭处理流程

    alt 正常关闭
        C->>F: 发送关闭帧
        F->>B: @OnClose触发
    else 异常关闭
        F->>B: @OnError触发
        F->>B: @OnClose触发
    end

    B->>S: 获取会话信息
    S->>B: 返回用户/设备信息

    B->>S: 清理会话数据
    B->>S: 更新在线状态

    B->>F: 广播离线消息
    F->>C: 通知其他客户端

    B->>M: 记录连接统计
    M->>B: 确认记录完成

    B->>S: 最终清理
    Note over B,S: 连接完全关闭
```

**聊天室关闭处理详细案例**：

```java
@OnClose
public void onClose(NettySession session) {
    String roomId = session.getAttribute("roomId");
    String username = session.getAttribute("username");
    Long joinTime = session.getAttribute("joinTime");

    if (roomId != null && username != null) {
        // 1. 计算在线时长
        long onlineDuration = System.currentTimeMillis() - joinTime;

        // 2. 从房间移除用户
        Set<NettySession> roomSessions = ROOMS.get(roomId);
        if (roomSessions != null) {
            roomSessions.remove(session);

            // 3. 广播用户离开消息
            ChatMessage leaveMessage = ChatMessage.builder()
                .type("system")
                .content(username + " 离开了聊天室")
                .timestamp(System.currentTimeMillis())
                .roomId(roomId)
                .onlineDuration(onlineDuration)
                .build();
            broadcastToRoom(roomId, leaveMessage);

            // 4. 房间为空时清理
            if (roomSessions.isEmpty()) {
                ROOMS.remove(roomId);
                roomService.markRoomInactive(roomId);
                log.info("房间已清空: roomId={}", roomId);
            }
        }

        // 5. 更新用户状态
        userService.setUserOffline(username, roomId);

        // 6. 记录用户活动日志
        userActivityService.recordUserActivity(username, roomId, joinTime, onlineDuration);

        // 7. 清理定时任务
        cancelUserActivityMonitor(session);

        log.info("用户离开房间: username={}, roomId={}, 在线时长={}ms",
                username, roomId, onlineDuration);
    }
}
```

### 生命周期最佳实践

#### 1. 资源管理策略

```java
@Component
public class WebSocketLifecycleManager {

    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private final Map<String, Long> connectionTimestamps = new ConcurrentHashMap<>();

    // 连接建立时的资源分配
    public void onConnectionEstablished(String sessionId, NettySession session) {
        // 记录连接时间
        connectionTimestamps.put(sessionId, System.currentTimeMillis());

        // 启动心跳检测任务
        ScheduledFuture<?> heartbeatTask = heartbeatExecutor.scheduleAtFixedRate(
            () -> checkHeartbeat(sessionId, session),
            30, 30, TimeUnit.SECONDS
        );
        scheduledTasks.put(sessionId, heartbeatTask);

        // 设置连接属性
        session.setAttribute("connectionTime", System.currentTimeMillis());
        session.setAttribute("lastActiveTime", System.currentTimeMillis());
    }

    // 连接关闭时的资源清理
    public void onConnectionClosed(String sessionId) {
        // 取消定时任务
        ScheduledFuture<?> task = scheduledTasks.remove(sessionId);
        if (task != null && !task.isCancelled()) {
            task.cancel(true);
        }

        // 清理连接记录
        connectionTimestamps.remove(sessionId);

        // 统计连接时长
        Long connectTime = connectionTimestamps.remove(sessionId);
        if (connectTime != null) {
            long duration = System.currentTimeMillis() - connectTime;
            connectionStatistics.recordConnectionDuration(sessionId, duration);
        }
    }
}
```

#### 2. 异常恢复机制

```java
@Component
public class ConnectionRecoveryManager {

    private final Map<String, Integer> retryCounters = new ConcurrentHashMap<>();
    private final int MAX_RETRY_COUNT = 3;

    public void handleConnectionError(String deviceId, Throwable error) {
        int retryCount = retryCounters.getOrDefault(deviceId, 0);

        if (retryCount < MAX_RETRY_COUNT) {
            // 增加重试计数
            retryCounters.put(deviceId, retryCount + 1);

            // 延迟重连
            scheduleReconnection(deviceId, calculateRetryDelay(retryCount));

        } else {
            // 达到最大重试次数，标记为永久失败
            log.error("设备连接重试次数超限: deviceId={}, maxRetry={}", deviceId, MAX_RETRY_COUNT);
            deviceManager.markDevicePermanentOffline(deviceId);
            retryCounters.remove(deviceId);
        }
    }

    private void scheduleReconnection(String deviceId, long delayMs) {
        scheduledExecutor.schedule(() -> {
            try {
                // 尝试重新建立连接
                deviceManager.attemptReconnection(deviceId);
                // 重连成功，清除重试计数
                retryCounters.remove(deviceId);

            } catch (Exception e) {
                log.warn("设备重连失败: deviceId={}", deviceId, e);
                // 重连失败，会再次触发错误处理
                handleConnectionError(deviceId, e);
            }
        }, delayMs, TimeUnit.MILLISECONDS);
    }

    private long calculateRetryDelay(int retryCount) {
        // 指数退避算法：1s, 2s, 4s, 8s...
        return Math.min(1000L << retryCount, 30000L); // 最大30秒
    }
}
```

这样的生命周期管理确保了 WebSocket 连接在各个阶段都能得到正确处理，提高了系统的稳定性和可靠性。

---

## 实战代码示例

### 基础聊天室示例

```java
@WebSocketEndpoint(path = "/chat/{roomId}")
@Component
public class ChatRoomEndpoint {

    // 房间会话管理
    private static final Map<String, Set<NettySession>> ROOMS = new ConcurrentHashMap<>();

    @BeforeHandshake
    public void beforeHandshake(NettySession session, HttpHeaders headers,
                               @PathVariable String roomId,
                               @RequestParam String username) {
        // 验证用户名
        if (StringUtils.isEmpty(username)) {
            session.close();
            return;
        }

        // 保存用户信息
        session.setAttribute("roomId", roomId);
        session.setAttribute("username", username);
        session.setAttribute("joinTime", System.currentTimeMillis());
    }

    @OnOpen
    public void onOpen(NettySession session, @PathVariable String roomId) {
        String username = session.getAttribute("username");

        // 加入房间
        ROOMS.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet())
              .add(session);

        // 广播用户加入消息
        ChatMessage joinMessage = ChatMessage.builder()
            .type("system")
            .content(username + " 加入了聊天室")
            .timestamp(System.currentTimeMillis())
            .build();

        broadcastToRoom(roomId, joinMessage);

        // 发送房间信息
        sendRoomInfo(session, roomId);
    }

    @OnMessage
    public void onMessage(NettySession session, String message) {
        String roomId = session.getAttribute("roomId");
        String username = session.getAttribute("username");

        try {
            // 解析消息
            ChatMessage chatMessage = JSON.parseObject(message, ChatMessage.class);
            chatMessage.setUsername(username);
            chatMessage.setTimestamp(System.currentTimeMillis());

            // 处理不同类型消息
            switch (chatMessage.getType()) {
                case "chat":
                    handleChatMessage(roomId, chatMessage);
                    break;
                case "ping":
                    handlePingMessage(session);
                    break;
                default:
                    log.warn("未知消息类型: {}", chatMessage.getType());
            }

        } catch (Exception e) {
            log.error("消息处理失败", e);
            sendErrorMessage(session, "消息格式错误");
        }
    }

    @OnClose
    public void onClose(NettySession session) {
        String roomId = session.getAttribute("roomId");
        String username = session.getAttribute("username");

        if (roomId != null) {
            // 离开房间
            Set<NettySession> roomSessions = ROOMS.get(roomId);
            if (roomSessions != null) {
                roomSessions.remove(session);

                // 广播用户离开消息
                ChatMessage leaveMessage = ChatMessage.builder()
                    .type("system")
                    .content(username + " 离开了聊天室")
                    .timestamp(System.currentTimeMillis())
                    .build();

                broadcastToRoom(roomId, leaveMessage);

                // 如果房间为空，清理房间
                if (roomSessions.isEmpty()) {
                    ROOMS.remove(roomId);
                }
            }
        }
    }

    @OnError
    public void onError(NettySession session, Throwable throwable) {
        String username = session.getAttribute("username");
        log.error("连接异常: username={}", username, throwable);
    }

    // 广播消息到房间
    private void broadcastToRoom(String roomId, ChatMessage message) {
        Set<NettySession> sessions = ROOMS.get(roomId);
        if (sessions != null) {
            String messageJson = JSON.toJSONString(message);
            sessions.forEach(session -> {
                if (session.isOpen()) {
                    session.sendText(messageJson);
                }
            });
        }
    }

    // 处理聊天消息
    private void handleChatMessage(String roomId, ChatMessage message) {
        // 内容过滤
        message.setContent(contentFilter.filter(message.getContent()));

        // 广播消息
        broadcastToRoom(roomId, message);

        // 保存聊天记录
        chatHistoryService.saveMessage(roomId, message);
    }

    // 发送房间信息
    private void sendRoomInfo(NettySession session, String roomId) {
        Set<NettySession> roomSessions = ROOMS.get(roomId);
        List<String> usernames = roomSessions.stream()
            .map(s -> (String) s.getAttribute("username"))
            .collect(Collectors.toList());

        RoomInfo roomInfo = RoomInfo.builder()
            .roomId(roomId)
            .userCount(usernames.size())
            .users(usernames)
            .build();

        session.sendText(JSON.toJSONString(roomInfo));
    }
}

// 消息对象
@Data
@Builder
public class ChatMessage {
    private String type;        // 消息类型
    private String username;    // 用户名
    private String content;     // 消息内容
    private long timestamp;     // 时间戳
}
```

### 物联网设备管理示例

```java
@WebSocketEndpoint(path = "/iot/device/{deviceId}")
@Component
public class IoTDeviceEndpoint {

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceDataProcessor dataProcessor;

    @BeforeHandshake
    public void beforeHandshake(NettySession session, HttpHeaders headers,
                               @PathVariable String deviceId) {
        // 设备认证
        String token = headers.get("Authorization");
        if (!deviceManager.validateDevice(deviceId, token)) {
            log.warn("设备认证失败: deviceId={}", deviceId);
            session.close();
            return;
        }

        // 检查设备状态
        if (!deviceManager.isDeviceActive(deviceId)) {
            log.warn("设备已被禁用: deviceId={}", deviceId);
            session.close();
            return;
        }

        session.setAttribute("deviceId", deviceId);
        session.setAttribute("authTime", System.currentTimeMillis());
    }

    @OnOpen
    public void onOpen(NettySession session, @PathVariable String deviceId) {
        // 注册设备连接
        deviceManager.registerConnection(deviceId, session);

        // 发送配置信息
        DeviceConfig config = deviceManager.getDeviceConfig(deviceId);
        sendDeviceConfig(session, config);

        // 启动心跳检测
        startHeartbeat(session, deviceId);

        log.info("设备连接成功: deviceId={}", deviceId);
    }

    @OnMessage
    public void onMessage(NettySession session, String message) {
        String deviceId = session.getAttribute("deviceId");

        try {
            DeviceMessage deviceMsg = JSON.parseObject(message, DeviceMessage.class);

            switch (deviceMsg.getType()) {
                case "heartbeat":
                    handleHeartbeat(deviceId, session);
                    break;
                case "data":
                    handleDeviceData(deviceId, deviceMsg.getData());
                    break;
                case "status":
                    handleStatusUpdate(deviceId, deviceMsg.getStatus());
                    break;
                case "alarm":
                    handleAlarm(deviceId, deviceMsg.getAlarm());
                    break;
                default:
                    log.warn("未知消息类型: deviceId={}, type={}",
                            deviceId, deviceMsg.getType());
            }

        } catch (Exception e) {
            log.error("设备消息处理失败: deviceId={}", deviceId, e);
            sendErrorResponse(session, "消息处理失败");
        }
    }

    @OnBinary
    public void onBinary(NettySession session, byte[] bytes) {
        String deviceId = session.getAttribute("deviceId");
        log.info("收到设备二进制数据: deviceId={}, size={}", deviceId, bytes.length);

        // 处理固件升级、配置文件等二进制数据
        dataProcessor.processBinaryData(deviceId, bytes);
    }

    @OnEvent
    public void onEvent(NettySession session, Object event) {
        String deviceId = session.getAttribute("deviceId");

        if (event instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) event;
            handleIdleEvent(deviceId, idleEvent, session);
        }
    }

    @OnClose
    public void onClose(NettySession session) {
        String deviceId = session.getAttribute("deviceId");
        deviceManager.unregisterConnection(deviceId);

        log.info("设备断开连接: deviceId={}", deviceId);
    }

    @OnError
    public void onError(NettySession session, Throwable throwable) {
        String deviceId = session.getAttribute("deviceId");
        log.error("设备连接异常: deviceId={}", deviceId, throwable);

        deviceManager.reportDeviceError(deviceId, throwable);
    }

    // 处理心跳
    private void handleHeartbeat(String deviceId, NettySession session) {
        deviceManager.updateLastHeartbeat(deviceId);

        // 回复心跳
        HeartbeatResponse response = HeartbeatResponse.builder()
            .timestamp(System.currentTimeMillis())
            .serverTime(System.currentTimeMillis())
            .build();

        session.sendText(JSON.toJSONString(response));
    }

    // 处理设备数据
    private void handleDeviceData(String deviceId, Object data) {
        // 数据验证
        if (!dataProcessor.validateData(deviceId, data)) {
            log.warn("设备数据验证失败: deviceId={}", deviceId);
            return;
        }

        // 异步处理数据
        CompletableFuture.runAsync(() -> {
            dataProcessor.processDeviceData(deviceId, data);
        });
    }

    // 启动心跳检测
    private void startHeartbeat(NettySession session, String deviceId) {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleAtFixedRate(() -> {
            if (session.isOpen()) {
                // 检查心跳超时
                long lastHeartbeat = deviceManager.getLastHeartbeat(deviceId);
                if (System.currentTimeMillis() - lastHeartbeat > 120000) { // 2分钟超时
                    log.warn("设备心跳超时: deviceId={}", deviceId);
                    session.close();
                }
            } else {
                executor.shutdown();
            }
        }, 60, 60, TimeUnit.SECONDS);
    }
}
```

---

## 应用场景与最佳实践

### 典型应用场景

#### 1. 实时聊天系统

```mermaid
graph TB
    subgraph "客户端"
        A[Web浏览器]
        B[移动App]
        C[桌面应用]
    end

    subgraph "服务端"
        D[WebSocket 服务器]
        E[消息路由器]
        F[用户管理]
        G[消息存储]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    E --> G

    classDef client fill:#e3f2fd
    classDef server fill:#f3e5f5

    class A,B,C client
    class D,E,F,G server
```

#### 2. 物联网设备监控

```mermaid
graph TB
    subgraph "设备层"
        A[温度传感器]
        B[湿度传感器]
        C[摄像头]
        D[智能门锁]
    end

    subgraph "网关层"
        E[边缘网关]
    end

    subgraph "云端服务"
        F[WebSocket 服务器]
        G[数据处理]
        H[设备管理]
        I[告警系统]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    F --> G
    F --> H
    G --> I
```

### 性能优化最佳实践

#### 1. 连接池管理

```java
@Component
public class ConnectionPoolManager {

    private final ConcurrentHashMap<String, ConnectionPool> pools = new ConcurrentHashMap<>();

    public static class ConnectionPool {
        private final String poolId;
        private final Set<NettySession> sessions;
        private final AtomicInteger connectionCount;
        private final long createTime;

        public ConnectionPool(String poolId) {
            this.poolId = poolId;
            this.sessions = ConcurrentHashMap.newKeySet();
            this.connectionCount = new AtomicInteger(0);
            this.createTime = System.currentTimeMillis();
        }

        public void addSession(NettySession session) {
            sessions.add(session);
            connectionCount.incrementAndGet();
        }

        public void removeSession(NettySession session) {
            if (sessions.remove(session)) {
                connectionCount.decrementAndGet();
            }
        }

        public void broadcast(String message) {
            sessions.parallelStream()
                   .filter(NettySession::isOpen)
                   .forEach(session -> session.sendText(message));
        }
    }

    public ConnectionPool getOrCreatePool(String poolId) {
        return pools.computeIfAbsent(poolId, ConnectionPool::new);
    }
}
```

#### 2. 消息队列缓冲

```java
@Component
public class MessageBuffer {

    private final BlockingQueue<Message> messageQueue = new LinkedBlockingQueue<>(10000);
    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(4);

    @PostConstruct
    public void startMessageProcessor() {
        // 批量处理消息
        executor.scheduleAtFixedRate(this::processMessages, 0, 100, TimeUnit.MILLISECONDS);
    }

    public void addMessage(Message message) {
        if (!messageQueue.offer(message)) {
            log.warn("消息队列已满，丢弃消息: {}", message);
        }
    }

    private void processMessages() {
        List<Message> messages = new ArrayList<>();
        messageQueue.drainTo(messages, 100); // 批量取出消息

        if (!messages.isEmpty()) {
            // 批量处理
            processBatch(messages);
        }
    }

    private void processBatch(List<Message> messages) {
        // 按目标分组
        Map<String, List<Message>> groupedMessages = messages.stream()
            .collect(Collectors.groupingBy(Message::getTarget));

        // 并行处理各组消息
        groupedMessages.entrySet().parallelStream()
            .forEach(entry -> {
                String target = entry.getKey();
                List<Message> targetMessages = entry.getValue();
                sendMessagesToTarget(target, targetMessages);
            });
    }
}
```

#### 3. 内存优化

```yaml
# JVM 参数优化
jvm:
  options: |
    -Xms2g
    -Xmx4g
    -XX:+UseG1GC
    -XX:MaxGCPauseMillis=200
    -XX:+UseStringDeduplication
    -XX:+OptimizeStringConcat

# Netty 优化配置
netty:
  websocket:
    # 直接内存使用
    use-direct-buffer: true
    # 内存池化
    use-pooled-buffer: true
    # 连接数限制
    max-connections: 50000
    # 缓冲区配置
    child-option-so-rcv-buf: 262144 # 256KB
    child-option-so-snd-buf: 262144 # 256KB
```

### 监控与运维

#### 1. 关键指标监控

```java
@Component
public class WebSocketMetrics {

    private final MeterRegistry meterRegistry;

    // 连接数指标
    private final AtomicInteger activeConnections = new AtomicInteger(0);

    // 消息指标
    private final Counter messagesReceived;
    private final Counter messagesSent;
    private final Timer messageProcessingTime;

    public WebSocketMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 注册指标
        Gauge.builder("websocket.connections.active")
             .description("当前活跃连接数")
             .register(meterRegistry, activeConnections, AtomicInteger::get);

        this.messagesReceived = Counter.builder("websocket.messages.received")
                                      .description("接收消息总数")
                                      .register(meterRegistry);

        this.messagesSent = Counter.builder("websocket.messages.sent")
                                  .description("发送消息总数")
                                  .register(meterRegistry);

        this.messageProcessingTime = Timer.builder("websocket.message.processing.time")
                                         .description("消息处理时间")
                                         .register(meterRegistry);
    }

    public void onConnectionOpen() {
        activeConnections.incrementAndGet();
    }

    public void onConnectionClose() {
        activeConnections.decrementAndGet();
    }

    public void onMessageReceived() {
        messagesReceived.increment();
    }

    public void onMessageSent() {
        messagesSent.increment();
    }

    public Timer.Sample startProcessingTimer() {
        return Timer.start(meterRegistry);
    }
}
```

#### 2. 健康检查

```java
@Component
public class WebSocketHealthIndicator implements HealthIndicator {

    @Autowired
    private ConnectionPoolManager connectionManager;

    @Override
    public Health health() {
        try {
            int activeConnections = connectionManager.getActiveConnectionCount();
            int totalPools = connectionManager.getPoolCount();

            Health.Builder builder = Health.up()
                .withDetail("activeConnections", activeConnections)
                .withDetail("totalPools", totalPools)
                .withDetail("timestamp", System.currentTimeMillis());

            // 连接数过多时标记为警告
            if (activeConnections > 40000) {
                builder.status("WARNING")
                       .withDetail("reason", "连接数接近上限");
            }

            return builder.build();

        } catch (Exception e) {
            return Health.down()
                        .withDetail("error", e.getMessage())
                        .build();
        }
    }
}
```

这份教程涵盖了从 WebSocket 协议基础到 netty-websocket-spring-boot-starter 框架的完整知识体系，包含了原理解析、实际代码示例和最佳实践，希望对您的学习和开发有所帮助！
