# 物联网设备 WebSocket 服务器

## 概述

基于 Spring Boot 2 + Netty 构建的高性能物联网设备 WebSocket 服务器，专为大量设备连接和实时数据传输而设计。

## 技术栈

- **Spring Boot 2.7.9** - 主框架
- **Netty 4.1.68** - 高性能网络框架
- **netty-websocket-spring-boot-starter** - WebSocket 集成
- **PostgreSQL** - 数据存储
- **Maven** - 项目管理

## 主要特性

### 🚀 高性能

- 基于 Netty 的异步非阻塞架构
- 支持万级并发连接
- 自定义线程池优化
- 内存零拷贝优化

### 🔒 安全可靠

- 设备认证机制
- Token 验证
- 连接状态监控
- 自动重连机制

### 💡 功能丰富

- 多设备并发连接
- 实时消息推送
- 心跳检测
- 设备数据上报
- 二进制数据传输
- 消息广播

### 📊 监控管理

- 在线设备统计
- 连接状态监控
- REST API 管理
- 实时日志

## 快速开始

### 1. 启动服务器

```bash
mvn spring-boot:run
```

### 2. 访问测试页面

打开浏览器访问：`http://localhost:8081/websocket-test.html`

### 3. 设备连接

WebSocket 连接地址：`ws://localhost:8080/iot/device/{deviceId}`

连接时需要在 Headers 中添加认证信息：

```
Authorization: device_token_123456
```

## API 接口

### 获取在线设备列表

```http
GET /api/iot/devices/online
```

### 向指定设备发送消息

```http
POST /api/iot/devices/send/{deviceId}
Content-Type: application/json

{
  "message": "Hello Device!"
}
```

### 广播消息到所有设备

```http
POST /api/iot/devices/broadcast
Content-Type: application/json

{
  "message": "Broadcast Message"
}
```

### 获取服务器状态

```http
GET /api/iot/devices/status
```

## 配置说明

### WebSocket 服务器配置

```yaml
netty:
  websocket:
    port: 8080 # WebSocket 端口
    host: 0.0.0.0 # 绑定地址
    boss-loop-group-threads: 1 # Boss 线程数
    worker-loop-group-threads: 0 # Worker 线程数（0=CPU核心数*2）

    # 连接配置
    option-connect-timeout-millis: 30000
    option-so-backlog: 1024
    child-option-tcp-nodelay: true
    child-option-so-keepalive: true

    # 心跳检测（秒）
    reader-idle-time-seconds: 60
    writer-idle-time-seconds: 60
    all-idle-time-seconds: 120

    # 消息帧配置
    max-frame-payload-length: 65536

    # 压缩支持
    use-compression-handler: true

    # 业务线程池
    use-event-executor-group: true
    event-executor-group-threads: 16
```

## 设备接入示例

### JavaScript 客户端

```javascript
const deviceId = "device001";
const token = "device_token_123456";
const wsUrl = `ws://localhost:8080/iot/device/${deviceId}`;

const websocket = new WebSocket(wsUrl);

websocket.onopen = function (event) {
  console.log("设备连接成功");

  // 发送心跳
  setInterval(() => {
    websocket.send("heartbeat");
  }, 30000);
};

websocket.onmessage = function (event) {
  console.log("收到消息:", event.data);
};

// 发送设备数据
function sendDeviceData() {
  const data = {
    type: "sensorData",
    temperature: 25.6,
    humidity: 60.2,
    timestamp: Date.now(),
  };
  websocket.send(JSON.stringify(data));
}
```

### Java 客户端

```java
@Component
public class IoTDeviceClient {

    private WebSocketSession session;

    @EventListener
    public void onOpen(SessionConnectedEvent event) {
        this.session = event.getSession();
        log.info("设备连接成功");

        // 启动心跳
        startHeartbeat();
    }

    public void sendDeviceData(Object data) {
        if (session != null && session.isOpen()) {
            session.sendMessage(new TextMessage(JSON.toJSONString(data)));
        }
    }

    private void startHeartbeat() {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleAtFixedRate(() -> {
            try {
                session.sendMessage(new TextMessage("heartbeat"));
            } catch (Exception e) {
                log.error("心跳发送失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
}
```

## 性能优化建议

### 1. 连接数优化

```yaml
netty:
  websocket:
    option-so-backlog: 2048 # 增加连接队列
    worker-loop-group-threads: 16 # 根据CPU调整
    event-executor-group-threads: 32 # 业务线程池
```

### 2. 内存优化

```yaml
netty:
  websocket:
    child-option-write-buffer-high-water-mark: 131072 # 128KB
    child-option-write-buffer-low-water-mark: 65536 # 64KB
    max-frame-payload-length: 131072 # 128KB
```

### 3. JVM 参数优化

```bash
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UnlockExperimentalVMOptions
-XX:+UseCGroupMemoryLimitForHeap
```

## 监控指标

### 关键监控项

- 在线设备数量
- 消息吞吐量（TPS）
- 连接建立/断开速率
- 内存使用情况
- CPU 使用率
- 网络 I/O

### 日志级别配置

```yaml
logging:
  level:
    com.hihonor.iot.ws.ls.listener: DEBUG # 业务日志
    cn.twelvet.websocket: INFO # 框架日志
    io.netty: WARN # Netty日志
```

## 故障排查

### 常见问题

1. **设备连接失败**

   - 检查认证 Token 是否正确
   - 确认网络连通性
   - 查看服务器日志

2. **消息丢失**

   - 检查心跳机制
   - 确认消息格式正确
   - 查看缓冲区配置

3. **性能问题**
   - 调整线程池大小
   - 优化垃圾回收
   - 监控系统资源

### 日志分析

```bash
# 查看连接日志
tail -f logs/application.log | grep "设备连接"

# 查看错误日志
tail -f logs/application.log | grep "ERROR"

# 监控性能
tail -f logs/application.log | grep "性能统计"
```

## 扩展功能

### 1. 集群部署

- 使用 Redis 共享会话
- 负载均衡配置
- 分布式锁管理

### 2. 数据持久化

- 消息存储到数据库
- 设备状态缓存
- 历史数据查询

### 3. 安全增强

- SSL/TLS 加密
- IP 白名单
- 访问频率限制

## 许可证

Apache License 2.0

## 联系方式

如有问题，请提交 Issue 或联系开发团队。
