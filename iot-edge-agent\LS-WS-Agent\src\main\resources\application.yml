spring:
  http:
    encoding:
      charset: UTF-8
  boot:
    admin:
      context-path: /admin
      monitor:
        threshold: 0.5
        status-interval: 30
  server:
    servlet:
      multipart:
        enabled: true
        max-file-size: 100MB

# WebSocket 服务器配置
netty:
  websocket:
    # 主端口配置
    port: 8080
    host: 0.0.0.0
    # 线程池配置
    boss-loop-group-threads: 1
    worker-loop-group-threads: 0  # 0 表示使用默认值（CPU核心数*2）
    
    # 连接超时配置（毫秒）
    option-connect-timeout-millis: 30000
    option-so-backlog: 1024
    
    # 子连接配置
    child-option-write-spin-count: 16
    child-option-write-buffer-high-water-mark: 65536
    child-option-write-buffer-low-water-mark: 32768
    child-option-tcp-nodelay: true
    child-option-so-keepalive: true
    child-option-so-linger: -1
    child-option-allow-half-closure: false
  
    # WebSocket 消息帧配置
    max-frame-payload-length: 65536  # 最大帧大小
    
    # 压缩配置
    use-compression-handler: true
    
    # 业务线程池配置
    use-event-executor-group: true
    event-executor-group-threads: 16
    
    # CORS 配置
    cors-origins: ["*"]
    cors-allow-credentials: true

# Spring Boot 基础配置
server:
  port: 8081  # HTTP 端口，与 WebSocket 端口分开

# 日志配置
logging:
  level:
    com.hihonor.iot.ws: DEBUG # 将com.hihonor.iot.ws包下的日志级别设为DEBUG，方便调试新模块
    cn.twelvet.websocket: INFO
    io.netty: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"






