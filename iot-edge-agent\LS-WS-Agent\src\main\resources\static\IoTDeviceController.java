package com.hihonor.iot.ws.ls.listener;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * 物联网设备管理 REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/iot/devices")
public class IoTDeviceController {

    @Autowired
    private IoTWebSocketServer webSocketServer;

    /**
     * 获取在线设备列表
     */
    @GetMapping("/online")
    public ResponseEntity<Map<String, Object>> getOnlineDevices() {
        Set<String> onlineDeviceIds = webSocketServer.getOnlineDeviceIds();
        int count = webSocketServer.getOnlineDeviceCount();

        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        result.put("deviceIds", onlineDeviceIds);
        result.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(result);
    }

    /**
     * 向指定设备发送消息
     */
    @PostMapping("/send/{deviceId}")
    public ResponseEntity<Map<String, Object>> sendToDevice(
            @PathVariable String deviceId,
            @RequestBody Map<String, Object> request) {

        String message = (String) request.get("message");
        boolean success = webSocketServer.sendToDevice(deviceId, message);

        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("deviceId", deviceId);
        result.put("message", success ? "消息发送成功" : "设备不在线或发送失败");

        return ResponseEntity.ok(result);
    }

    /**
     * 向所有设备广播消息
     */
    @PostMapping("/broadcast")
    public ResponseEntity<Map<String, Object>> broadcastToAllDevices(
            @RequestBody Map<String, Object> request) {

        String message = (String) request.get("message");
        webSocketServer.broadcastToAllDevices(message);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("deviceCount", webSocketServer.getOnlineDeviceCount());
        result.put("message", "广播消息发送成功");

        return ResponseEntity.ok(result);
    }

    /**
     * 获取服务器状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServerStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("onlineDeviceCount", webSocketServer.getOnlineDeviceCount());
        status.put("serverTime", System.currentTimeMillis());
        status.put("status", "running");

        return ResponseEntity.ok(status);
    }
}