package com.hihonor.iot.ws.connection;

/**
 * 设备信息类
 * 存储连接设备的详细信息
 */
public class DeviceInfo {
    private String resourceId;
    private long connectTime;
    private long lastMessageTime; // 修改：之前为 lastHeartbeat，根据需求改为 lastMessageTime

    public DeviceInfo(String resourceId) {
        this.resourceId = resourceId;
        this.connectTime = System.currentTimeMillis();
        this.lastMessageTime = System.currentTimeMillis();
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public long getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(long connectTime) {
        this.connectTime = connectTime;
    }

    public long getLastMessageTime() {
        return lastMessageTime;
    }

    public void setLastMessageTime(long lastMessageTime) {
        this.lastMessageTime = lastMessageTime;
    }

    @Override
    public String toString() {
        return "DeviceInfo{" +
                "resourceId='" + resourceId + '\'' +
                ", connectTime=" + connectTime +
                ", lastMessageTime=" + lastMessageTime +
                '}';
    }
}