
#
logging:
  config: classpath:logback-spring.xml


# 测试环境使用pekctxt
docker_region: ctyun-cn-north1
# 应用配置环境
docker_env: pro
# 应用信息
application:
  # IAM项目ID，应用ID
  appId: 51a5b1d937a249a7b817c70acf7b8b33
  # 部署单元
  subAppId: ls-ws-agent

spring:
  application:
    name: ls-ws-agent
  profiles:
    active: prod
  cloud:
    nacos:
      config:
        endpoint: mse.yun.hihonor.com:80
        context-path: /nacos-address/service/nacos
    compatibility-verifier:
      enabled: false
truss:
  apimall:
    enterprise: 99999999999999999999999999999999
    ## 生产为：https://yun.hihonor.com
    endpoint: https://yun.hihonor.com
    ## apimall中注册的API集成账号
    project: 51a5b1d937a249a7b817c70acf7b8b33
    account: 51a5b1d937a249a7b817c70acf7b8b33
    secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd


iam:
  enterprise: 99999999999999999999999999999999
  project: 51a5b1d937a249a7b817c70acf7b8b33
  endpoint: http://apig.heds.hihonor.com/api
  account: 51a5b1d937a249a7b817c70acf7b8b33
  secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd


apimall:
  consumer: true
  endpoint: https://yun.hihonor.com
  account: 51a5b1d937a249a7b817c70acf7b8b33
  secret: 4STmH/pAYJZ7U6i3Xci3tPMOc0bu2V/le1TH56zd

