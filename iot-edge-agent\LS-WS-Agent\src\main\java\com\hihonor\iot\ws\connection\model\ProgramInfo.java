package com.hihonor.iot.ws.connection.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 程序信息，用于 SET_PROGRAM_TASK 消息中的 programs 数组。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgramInfo {

    /**
     * 轨道号。
     * (String, Mandatory, e.g., "1", "2")
     */
    private String trackId;

    /**
     * 程序名称，可能包含中文。
     * (String, Mandatory, e.g., "HN2DNNSU001-C-Pass")
     */
    private String programName;
}