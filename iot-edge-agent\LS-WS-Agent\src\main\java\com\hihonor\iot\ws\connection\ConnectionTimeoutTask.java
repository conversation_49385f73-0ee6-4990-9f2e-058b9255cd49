package com.hihonor.iot.ws.connection;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.twelvet.websocket.netty.domain.NettySession;

/**
 * 定时任务，用于检查并处理长时间无活动的WebSocket连接。
 * 
 * <AUTHOR> AI
 * @since 2025-06-03
 */
@Component
public class ConnectionTimeoutTask {
    private static final Logger log = LoggerFactory.getLogger(ConnectionTimeoutTask.class);
    private static final long TIMEOUT_MS = 90 * 1000L; // 恢复为90秒超时时间

    private final DeviceSessionManager deviceSessionManager;

    @Autowired
    public ConnectionTimeoutTask(DeviceSessionManager deviceSessionManager) {
        this.deviceSessionManager = deviceSessionManager;
    }

    /**
     * 定期检查连接超时。
     * 每15秒执行一次。
     */
    @Scheduled(fixedRate = 30000) // 每30秒执行一次 (30000毫秒)
    public void checkConnectionTimeouts() {
        log.debug("开始执行连接超时检查任务...");
        Map<String, DeviceInfo> allDeviceInfos = deviceSessionManager.getAllDeviceInfos();
        long currentTime = System.currentTimeMillis();

        // 遍历设备信息Map的副本进行检查，以避免在迭代时直接修改原始Map导致ConcurrentModificationException
        // getAllDeviceInfos() 当前返回的是引用，但DeviceSessionManager内部使用的是ConcurrentHashMap，
        // 对于迭代ConcurrentHashMap的values()或entrySet()是弱一致性的，不会抛出CME。
        // 但如果要在迭代中移除，最好使用iterator.remove()或在迭代结束后统一移除。
        // 这里选择迭代key，然后根据key获取session并关闭，再通过manager反注册。

        if (allDeviceInfos.isEmpty()) {
            log.debug("当前没有活动的设备连接，跳过超时检查。");
            return;
        }

        // 使用entrySet进行迭代以同时获取resourceid和DeviceInfo
        for (Map.Entry<String, DeviceInfo> entry : allDeviceInfos.entrySet()) {
            String resourceid = entry.getKey();
            DeviceInfo deviceInfo = entry.getValue();

            if (deviceInfo == null) {
                log.warn("发现resourceid '{}' 对应的DeviceInfo为null，跳过此条目。", resourceid);
                continue;
            }

            if (currentTime - deviceInfo.getLastMessageTime() > TIMEOUT_MS) {
                log.warn("设备连接超时: resourceid={}, 最后活动时间: {}, 当前时间: {}. 将主动断开连接。",
                        resourceid, new java.util.Date(deviceInfo.getLastMessageTime()),
                        new java.util.Date(currentTime));

                NettySession sessionToClose = deviceSessionManager.getSessionByResourceid(resourceid);
                if (sessionToClose != null) {
                    if (sessionToClose.isOpen()) {
                        try {
                            sessionToClose.close(); // 关闭会话
                            log.info("已关闭超时的设备会话: resourceid={}", resourceid);
                        } catch (Exception e) {
                            log.error("关闭超时会话时发生错误: resourceid={}", resourceid, e);
                        }
                    }
                    // 不论session是否已关闭，都尝试从管理器中注销，以清理状态
                    // DeviceSessionManager的unregisterSession会处理session可能已关闭的情况
                    deviceSessionManager.unregisterSession(sessionToClose);
                } else {
                    log.warn("尝试关闭超时连接，但未找到 resourceid={} 对应的活动会话。可能已被其他方式关闭。", resourceid);
                    // 如果session为null，但deviceInfoMap中还有记录，也尝试清理deviceInfoMap和sessionsByResourceid
                    // DeviceSessionManager.unregisterSession(NettySession) 依赖
                    // session，如果session为null则无法工作
                    // 此时，需要一个按 resourceid 清理的方法，或者确保 unregisterSession 能处理这种情况
                    // 鉴于当前的 unregisterSession 实现，如果 sessionToClose 为
                    // null，则无法清理。这可能需要后续优化DeviceSessionManager
                    // 但通常情况下，deviceInfo 和 session 应该是一致的。
                }
            }
        }
        log.debug("连接超时检查任务执行完毕。当前在线设备数: {}", deviceSessionManager.getOnlineDeviceCount());
    }
}