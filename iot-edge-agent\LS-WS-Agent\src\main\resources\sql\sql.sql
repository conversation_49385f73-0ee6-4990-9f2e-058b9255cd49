/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

CREATE TABLE iot_admin.plate_device
(
    id        SERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    data      TEXT         NOT NULL
);
CREATE INDEX idx_plate_device_device_id ON iot_admin.plate_device (device_id);
CREATE INDEX idx_plate_device_timestamp ON iot_admin.plate_device (timestamp);
CREATE INDEX idx_plate_device_device_id_timestamp ON iot_admin.plate_device (device_id, timestamp);


CREATE TABLE iot_admin.transfer_board_info (
                                               id SERIAL PRIMARY KEY,
                                               name VARCHAR(255) NOT NULL,
                                               ip_port VARCHAR(255) NOT NULL,
                                               description TEXT
);


-- CREATE TABLE iot_admin.plc_operation_log (
--                                id SERIAL PRIMARY KEY,
--                                device_type VARCHAR(50),
--                                device_name VARCHAR(255),
--                                online_status BOOLEAN,
--                                method_name VARCHAR(255) NOT NULL,
--                                method_params JSONB,
--                                user_id VARCHAR(255),
--                                operation_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--                                execution_time INTEGER NOT NULL,
--                                return_value JSONB,
--                                exception_message TEXT
-- );
-- -- 为operation_time字段创建索引
-- CREATE INDEX iot_admin.idx_operation_log_operation_time ON iot_admin.plc_operation_log (operation_time);
--
-- -- 为method_name字段创建索引
-- CREATE INDEX iot_admin.idx_operation_log_method_name ON iot_admin.plc_operation_log (method_name);
--
-- -- 为operation_time和method_name字段创建复合索引,时间列在前
-- CREATE INDEX iot_admin.idx_operation_log_time_method ON iot_admin.plc_operation_log (operation_time, method_name);


-- 创建主表
CREATE TABLE iot_admin.plc_operation_log (
                                             id SERIAL,
                                             device_type VARCHAR(50),
                                             device_name VARCHAR(255),
                                             online_status BOOLEAN,
                                             method_name VARCHAR(255) NOT NULL,
                                             method_params JSONB,
                                             user_id VARCHAR(255),
                                             operation_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                             execution_time INTEGER NOT NULL,
                                             return_value JSONB,
                                             exception_message TEXT
) PARTITION BY RANGE (operation_time);




-- 创建单独的索引
CREATE INDEX idx_operation_log_device_name ON iot_admin.plc_operation_log (device_name);
CREATE INDEX idx_operation_log_method_name ON iot_admin.plc_operation_log (method_name);
CREATE INDEX idx_operation_log_operation_time ON iot_admin.plc_operation_log (operation_time);

-- 创建联合索引
-- 覆盖设备名查询
CREATE INDEX idx_operation_log_device_name_method ON iot_admin.plc_operation_log (device_name, method_name);
-- 覆盖设备名和方法名查询
CREATE INDEX idx_operation_log_device_name_method_time ON iot_admin.plc_operation_log (device_name, method_name, operation_time);



CREATE OR REPLACE FUNCTION iot_admin.create_plc_operation_log_partition() RETURNS void AS $$
DECLARE
last_partition_name text;
    last_partition_date date;
    new_partition_date date;
    new_partition_name text;
BEGIN
    -- 获取最后一个分区的名称
SELECT c.relname
FROM pg_inherits i
         JOIN pg_class c ON i.inhrelid = c.oid
         JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE i.inhparent = 'iot_admin.plc_operation_log'::regclass AND n.nspname = 'iot_admin'
ORDER BY c.relname DESC
    LIMIT 1
INTO last_partition_name;

IF last_partition_name IS NULL THEN
        -- 如果没有找到任何分区,则创建当天的分区
        new_partition_date := CURRENT_DATE;
ELSE
        -- 如果找到了最后一个分区,则提取其日期部分,并计算下一天的日期
        last_partition_date := to_date(substr(last_partition_name, 19, 10), 'YYYY_MM_DD');
        new_partition_date := last_partition_date + INTERVAL '1 day';
END IF;

    -- 生成新分区的名称
    new_partition_name := 'plc_operation_log_' || to_char(new_partition_date, 'YYYY_MM_DD');

    -- 创建新分区
EXECUTE format('CREATE TABLE IF NOT EXISTS iot_admin.%I PARTITION OF iot_admin.plc_operation_log FOR VALUES FROM (%L) TO (%L)',
               new_partition_name,
               new_partition_date,
               new_partition_date + INTERVAL '1 day');

-- 为新分区创建索引
EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%s_operation_time ON iot_admin.%I (operation_time)', new_partition_name, new_partition_name);
EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%s_method_name ON iot_admin.%I (method_name)', new_partition_name, new_partition_name);
EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%s_time_method ON iot_admin.%I (operation_time, method_name)', new_partition_name, new_partition_name);

RAISE NOTICE '分区 % 已创建', new_partition_name;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION iot_admin.drop_plc_operation_log_partition(IN partition_date DATE) RETURNS void AS $$
DECLARE
partition_name TEXT;
BEGIN
    partition_name := 'plc_operation_log_' || to_char(partition_date, 'YYYY_MM_DD');

    -- 检查分区是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_schema = 'iot_admin' AND table_name = partition_name) THEN
        -- 删除分区
        EXECUTE format('DROP TABLE iot_admin.%I', partition_name);
        RAISE NOTICE '分区 % 已删除', partition_name;
ELSE
        RAISE NOTICE '分区 % 不存在,跳过删除', partition_name;
END IF;
END;
$$ LANGUAGE plpgsql;
SELECT iot_admin.drop_plc_operation_log_partition('2024-05-17');

SELECT iot_admin.create_plc_operation_log_partition();







TRUNCATE TABLE iot_admin.plc_operation_log;


CREATE OR REPLACE FUNCTION iot_admin.insert_random_plc_operation_log(p_date DATE)
    RETURNS VOID AS $$
DECLARE
v_device_type VARCHAR(50);
    v_device_name VARCHAR(255);
    v_online_status BOOLEAN;
    v_method_name VARCHAR(255);
    v_method_params JSONB;
    v_user_id VARCHAR(255);
    v_execution_time INTEGER;
    v_return_value JSONB;
    v_exception_message TEXT;
BEGIN
    -- 生成随机数据
    v_device_type := 'PLC_' || (random() * 10)::INTEGER;
    v_device_name := 'Device_' || (random() * 100)::INTEGER;
    v_online_status := (CASE WHEN random() < 0.8 THEN true ELSE false END);
    v_method_name := 'Method_' || (random() * 50)::INTEGER;
    v_method_params := jsonb_build_object('param1', 'value1', 'param2', (random() * 100)::INTEGER);
    v_user_id := 'User_' || (random() * 10)::INTEGER;
    v_execution_time := (random() * 1000)::INTEGER;
    v_return_value := jsonb_build_object('result', 'success');
    v_exception_message := (CASE WHEN random() < 0.1 THEN 'Exception occurred' ELSE NULL END);

    -- 插入数据
INSERT INTO iot_admin.plc_operation_log (
    device_type, device_name, online_status, method_name,
    method_params, user_id, operation_time, execution_time,
    return_value, exception_message
)
VALUES (
           v_device_type, v_device_name, v_online_status, v_method_name,
           v_method_params, v_user_id, p_date, v_execution_time,
           v_return_value, v_exception_message
       );

-- 记录日志
RAISE NOTICE '已在 % 插入一条随机数据', p_date;
END;
$$ LANGUAGE plpgsql;

SELECT iot_admin.insert_random_plc_operation_log('2024-04-01');


CREATE OR REPLACE FUNCTION iot_admin.insert_random_data_into_partitions(p_start_date DATE, p_end_date DATE, p_num_rows INTEGER)
    RETURNS VOID AS $$
DECLARE
v_current_date DATE := p_start_date;
    v_partition_name TEXT;
BEGIN
    WHILE v_current_date <= p_end_date LOOP
            -- 获取当前日期对应的分区名称
SELECT partition_name INTO v_partition_name
FROM (
         SELECT p.relname AS partition_name
         FROM pg_inherits i
                  JOIN pg_class c ON i.inhrelid = c.oid
                  JOIN pg_class p ON i.inhparent = p.oid
         WHERE p.relname LIKE 'plc_operation_log_%' AND p.relname ~ ('.*' || v_current_date || '.*')
         ORDER BY p.relname
             LIMIT 1
     ) current_partition;

-- 向当前分区插入数据
FOR i IN 1..p_num_rows LOOP
                    EXECUTE format('SELECT iot_admin.insert_random_plc_operation_log(''%s'')', v_current_date);
END LOOP;

            v_current_date := v_current_date + INTERVAL '1 day';
END LOOP;
END;
$$ LANGUAGE plpgsql;

SELECT iot_admin.insert_random_data_into_partitions('2024-04-01', '2024-04-05', 5);