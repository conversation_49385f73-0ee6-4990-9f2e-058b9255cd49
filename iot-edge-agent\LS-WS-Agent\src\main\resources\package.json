{
  "attribute": [
    {
      "type": "point",
      //point类型表示点位信息;如果为node,表示部件，部件的含义它是单独的设备，属于机器的某个部门，它包含点位信息，或子部件
      "point": {
        "name": "BuzzerSwitch",
        "type": "singleAddress",
        "adress": "XXXXX"
      }
    },
    {
      "type": "node",
      "name": "1track",
      "attribute": [
        {
          "type": "point",
          "point": {
            "name": "status",
            "type": "singleAddress",
            "adress": "XXXXX"
          }
        },
        {
          "type": "point",
          "point": {
            "name": "trackBoardStatus",
            "type": "singleAddress",
            "adress": "XXXXX"
          }
        },
        {
          "type": "node",
          "name": "1layer",
          "attribute": [
            {
              "type": "point",
              "point": {
                "name": "status",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "rangAddress",
                "startAdress": "XXXXX",
                "endAdress": "XXXXX"
              }
            }
          ]
        },
        {
          "type": "node",
          "name": "2layer",
          "attribute": [
            {
              "type": "point",
              "point": {
                "name": "status",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "barcode",
                "type": "rangAddress",
                "startAdress": "XXXXX",
                "endAdress": "XXXXX"
              }
            }
          ]
        }
      ]
    },
    {
      "type": "node",
      "name": "2track",
      "attribute": [
        {
          "type": "point",
          "point": {
            "name": "status",
            "type": "singleAddress",
            "adress": "XXXXX"
          }
        },
        {
          "type": "point",
          "point": {
            "name": "trackBoardStatus",
            "type": "singleAddress",
            "adress": "XXXXX"
          }
        },
        {
          "type": "node",
          "name": "1layer",
          "attribute": [
            {
              "type": "point",
              "point": {
                "name": "status",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "rangAddress",
                "startAdress": "XXXXX",
                "endAdress": "XXXXX"
              }
            }
          ]
        },
        {
          "type": "node",
          "name": "2layer",
          "attribute": [
            {
              "type": "point",
              "point": {
                "name": "status",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "time",
                "type": "singleAddress",
                "adress": "XXXXX"
              }
            },
            {
              "type": "point",
              "point": {
                "name": "barcode",
                "type": "rangAddress",
                "startAdress": "XXXXX",
                "endAdress": "XXXXX"
              }
            }
          ]
        }
      ]
    }
  ]
}