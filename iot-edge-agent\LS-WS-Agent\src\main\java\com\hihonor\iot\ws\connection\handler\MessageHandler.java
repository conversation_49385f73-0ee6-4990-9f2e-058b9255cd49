package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;

import cn.twelvet.websocket.netty.domain.NettySession;

/**
 * WebSocket 消息处理器接口。
 * 每个实现类负责处理一种特定类型的消息。
 */
public interface MessageHandler {

    /**
     * 处理特定类型的消息。
     *
     * @param session          当前的 NettySession
     * @param resourceid       设备的 resourceid
     * @param processedMessage 已经过初步处理（如 timestamp 填充）的 BaseWebSocketMessage，
     *                         其 data 字段为 ObjectNode，需要 Handler 内部进一步解析。
     * @return 需要回复给客户端的消息对象 (BaseWebSocketMessage)，如果不需要回复，则返回 null。
     * @throws JsonProcessingException 如果 Handler 内部解析 dataNode 失败。
     */
    BaseWebSocketMessage<?> handle(NettySession session, String resourceid,
            BaseWebSocketMessage<ObjectNode> processedMessage) throws JsonProcessingException;

    /**
     * 获取此 Handler 负责处理的消息类型字符串。
     *
     * @return 消息类型字符串 (例如 "HEARTBEAT", "PROGRAM_TASK_RESULT")
     */
    String getMessageTypeHandled();
}