package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;
import com.hihonor.iot.ws.connection.model.HeartbeatData;

import cn.twelvet.websocket.netty.domain.NettySession;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 心跳消息处理器
 * 处理设备发送的心跳消息，并回复心跳确认
 */
@Component
@Slf4j  
public class HeartbeatMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(HeartbeatMessageHandler.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public BaseWebSocketMessage<?> handle(NettySession session, String resourceid, 
            BaseWebSocketMessage<ObjectNode> processedMessage) throws JsonProcessingException {
        
        log.info("收到来自 resourceid={} 的心跳消息。Timestamp: {}", resourceid, processedMessage.getTimestamp());
     
        // 解析心跳数据（可选）
        HeartbeatData heartbeatData = null;
        ObjectNode dataNode = processedMessage.getData();
        if (dataNode != null && !dataNode.isNull()) {
            heartbeatData = objectMapper.treeToValue(dataNode, HeartbeatData.class);
            log.debug("心跳数据: {}", heartbeatData);
        }
        
        // 创建心跳确认响应
        Map<String, Object> ackData = Map.of(
            "status", "alive",
            "serverTime", System.currentTimeMillis(),
            "message", "心跳确认"
        );
        
        BaseWebSocketMessage<Map<String, Object>> heartbeatAck = new BaseWebSocketMessage<>(
            "HEARTBEAT_ACK",
            null, // Timestamp会由发送方法自动填充
            processedMessage.getMessageId(), // 回传原始messageId
            ackData
        );
        
        log.debug("向 resourceid={} 发送心跳确认", resourceid);
        return heartbeatAck;
    }
    
    @Override
    public String getMessageTypeHandled() {
        return "HEARTBEAT";
    }
}
