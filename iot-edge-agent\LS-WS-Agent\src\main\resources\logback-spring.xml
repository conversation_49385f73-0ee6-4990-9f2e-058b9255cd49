<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
  -->

<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <property name="LOG_HOME" value="${user.dir}/logs" />
    <property name="LOG_NAME" value="root"></property>
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} %line - %msg%n"/>

    <!--输出到控制台 ConsoleAppender-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--展示格式 layout-->
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <appender name="TIME_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--日志名称，如果没有File 属性，那么只会使用FileNamePattern的文件路径规则
            如果同时有<File>和<FileNamePattern>，那么当天日志是<File>，明天会自动把今天
            的日志改名为今天的日期。即，<File> 的日志都是当天的。
        -->
        <File>${LOG_HOME}/${LOG_NAME}.log</File>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
            <FileNamePattern>${LOG_HOME}/${LOG_NAME}.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!--只保留最近90天的日志-->
            <maxHistory>30</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <totalSizeCap>1GB</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--日志输出编码格式化-->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>
<!--    <logger name="com.hihonor.iot.plc" additivity="false" level="DEBUG">-->
<!--        <appender-ref ref="CONSOLE"/>-->
<!--        <appender-ref ref="TIME_FILE"/>-->
<!--    </logger>-->
<!--    <logger name="com.baomidou.mybatisplus" level="OFF"/>-->
<!--    <logger name="com.thingworx" additivity="false" level="INFO">-->
<!--        <appender-ref ref="CONSOLE"/>-->
<!--        <appender-ref ref="TIME_FILE"/>-->
<!--    </logger>-->
<!--    &lt;!&ndash;指定最基础的日志输出级别&ndash;&gt;-->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TIME_FILE"/>
    </root>
<!--    <logger name="org.mybatis" level="DEBUG"/>-->
<!--    <logger name="com.baomidou.mybatisplus" level="DEBUG"/>-->
<!--    <logger name="com.hihonr.iot.cachingmachine.repo.PlateDeviceMapper" level="DEBUG"/>-->


</configuration>