package com.hihonor.iot.ws.connection.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试BaseWebSocketMessage的时间戳处理
 */
public class BaseWebSocketMessageTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testTimestampSerialization() throws Exception {
        // 创建一个带有UTC时间戳的消息
        Long currentTimestamp = System.currentTimeMillis();
        BaseWebSocketMessage<String> message = new BaseWebSocketMessage<>(
            "TEST_MESSAGE",
            currentTimestamp,
            "test-message-id",
            "test data"
        );

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(message);
        System.out.println("序列化后的JSON: " + json);

        // 验证JSON包含数字类型的时间戳
        assertTrue(json.contains("\"timestamp\":" + currentTimestamp));
        assertFalse(json.contains("\"timestamp\":\"" + currentTimestamp + "\""));

        // 反序列化
        BaseWebSocketMessage<?> deserializedMessage = objectMapper.readValue(json, BaseWebSocketMessage.class);
        
        // 验证时间戳类型和值
        assertNotNull(deserializedMessage.getTimestamp());
        assertEquals(currentTimestamp, deserializedMessage.getTimestamp());
        assertTrue(deserializedMessage.getTimestamp() instanceof Long);
    }

 

    @Test
    public void testNullTimestamp() throws Exception {
        // 测试时间戳为null的情况
        BaseWebSocketMessage<String> message = new BaseWebSocketMessage<>(
            "TEST_MESSAGE",
            null,
            "test-message-id",
            "test data"
        );

        String json = objectMapper.writeValueAsString(message);
        System.out.println("null时间戳序列化后的JSON: " + json);

        // 由于使用了@JsonInclude(JsonInclude.Include.NON_NULL)，null字段不会出现在JSON中
        assertFalse(json.contains("timestamp"));

        BaseWebSocketMessage<?> deserializedMessage = objectMapper.readValue(json, BaseWebSocketMessage.class);
        assertNull(deserializedMessage.getTimestamp());
    }
}
