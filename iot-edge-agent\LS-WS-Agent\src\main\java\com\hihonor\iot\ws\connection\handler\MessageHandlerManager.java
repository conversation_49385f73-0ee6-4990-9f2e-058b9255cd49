package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;

import cn.twelvet.websocket.netty.domain.NettySession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息处理器管理器
 * 负责管理和路由不同类型的消息到对应的处理器
 */
@Component
public class MessageHandlerManager {
    
    private static final Logger log = LoggerFactory.getLogger(MessageHandlerManager.class);
    
    @Autowired
    private List<MessageHandler> messageHandlers;
    
    private final Map<String, MessageHandler> handlerMap = new HashMap<>();
    
    @PostConstruct
    public void initializeHandlers() {
        log.info("初始化消息处理器...");
        
        for (MessageHandler handler : messageHandlers) {
            String messageType = handler.getMessageTypeHandled();
            if (handlerMap.containsKey(messageType)) {
                log.warn("发现重复的消息类型处理器: {}, 已存在: {}, 新的: {}", 
                    messageType, handlerMap.get(messageType).getClass().getSimpleName(), 
                    handler.getClass().getSimpleName());
            }
            handlerMap.put(messageType, handler);
            log.info("注册消息处理器: {} -> {}", messageType, handler.getClass().getSimpleName());
        }
        
        log.info("消息处理器初始化完成，共注册 {} 个处理器", handlerMap.size());
    }
    
    /**
     * 处理消息
     * 
     * @param session 会话
     * @param resourceid 设备ID
     * @param message 已处理的消息对象
     * @return 响应消息，如果不需要响应则返回null
     */
    public BaseWebSocketMessage<?> handleMessage(NettySession session, String resourceid, 
            BaseWebSocketMessage<ObjectNode> message) {
        
        String messageType = message.getType();
        if (messageType == null || messageType.trim().isEmpty()) {
            log.warn("收到来自 resourceid={} 的消息类型为空的消息", resourceid);
            return createErrorResponse(message.getMessageId(), "消息类型为空");
        }
        
        MessageHandler handler = handlerMap.get(messageType.toUpperCase());
        if (handler == null) {
            log.warn("收到来自 resourceid={} 的未知消息类型: {}", resourceid, messageType);
            return createErrorResponse(message.getMessageId(), "未知的消息类型: " + messageType);
        }
        
        try {
            log.debug("使用处理器 {} 处理来自 resourceid={} 的消息类型: {}", 
                handler.getClass().getSimpleName(), resourceid, messageType);
            
            BaseWebSocketMessage<?> response = handler.handle(session, resourceid, message);
            
            if (response != null) {
                log.debug("处理器 {} 返回响应消息类型: {}", 
                    handler.getClass().getSimpleName(), response.getType());
            } else {
                log.debug("处理器 {} 未返回响应消息", handler.getClass().getSimpleName());
            }
            
            return response;
            
        } catch (JsonProcessingException e) {
            log.error("处理器 {} 解析消息时发生JSON错误 - resourceid={}, messageType={}", 
                handler.getClass().getSimpleName(), resourceid, messageType, e);
            return createErrorResponse(message.getMessageId(), "消息格式错误: " + e.getMessage());
            
        } catch (Exception e) {
            log.error("处理器 {} 处理消息时发生错误 - resourceid={}, messageType={}", 
                handler.getClass().getSimpleName(), resourceid, messageType, e);
            return createErrorResponse(message.getMessageId(), "消息处理错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取支持的消息类型列表
     */
    public String[] getSupportedMessageTypes() {
        return handlerMap.keySet().toArray(new String[0]);
    }
    
    /**
     * 检查是否支持指定的消息类型
     */
    public boolean isMessageTypeSupported(String messageType) {
        return messageType != null && handlerMap.containsKey(messageType.toUpperCase());
    }
    
    /**
     * 获取指定消息类型的处理器
     */
    public MessageHandler getHandler(String messageType) {
        return messageType != null ? handlerMap.get(messageType.toUpperCase()) : null;
    }
    
    /**
     * 创建错误响应消息
     */
    private BaseWebSocketMessage<Map<String, Object>> createErrorResponse(String messageId, String errorMessage) {
        Map<String, Object> errorData = Map.of(
            "status", "error",
            "error", errorMessage,
            "timestamp", System.currentTimeMillis()
        );
        
        return new BaseWebSocketMessage<>(
            "MESSAGE_ERROR",
            null,
            messageId,
            errorData
        );
    }
    
    /**
     * 获取处理器统计信息
     */
    public Map<String, String> getHandlerStatistics() {
        Map<String, String> stats = new HashMap<>();
        for (Map.Entry<String, MessageHandler> entry : handlerMap.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().getClass().getSimpleName());
        }
        return stats;
    }
}
