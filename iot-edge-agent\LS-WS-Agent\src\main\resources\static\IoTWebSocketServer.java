package com.hihonor.iot.ws.ls.listener;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import cn.twelvet.websocket.netty.annotation.BeforeHandshake;
import cn.twelvet.websocket.netty.annotation.OnBinary;
import cn.twelvet.websocket.netty.annotation.OnClose;
import cn.twelvet.websocket.netty.annotation.OnError;
import cn.twelvet.websocket.netty.annotation.OnEvent;
import cn.twelvet.websocket.netty.annotation.OnMessage;
import cn.twelvet.websocket.netty.annotation.OnOpen;
import cn.twelvet.websocket.netty.domain.NettySession;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * 物联网设备 WebSocket 服务器
 * 支持多设备连接、心跳检测、消息广播等功能
 */
@Slf4j
@Component
// @WebSocketEndpoint(path = "/iot/device", port = "${netty.websocket.port}")
public class IoTWebSocketServer {

    /**
     * 存储设备连接会话
     */
    private static final Map<String, NettySession> DEVICE_SESSIONS = new ConcurrentHashMap<>();

    /**
     * 存储设备信息
     */
    private static final Map<String, DeviceInfo> DEVICE_INFO_MAP = new ConcurrentHashMap<>();

    /**
     * 握手阶段验证设备
     */
    @BeforeHandshake
    public void beforeHandshake(NettySession session, HttpHeaders headers) {
        // 从请求头获取设备ID
        String deviceId = headers.get("Device-Id");
        if (!StringUtils.hasText(deviceId)) {
            // 也可以从查询参数获取（如果支持的话）
            deviceId = "device_" + System.currentTimeMillis();
        }

        log.info("设备尝试连接: deviceId={}", deviceId);

        // 验证设备 token
        String token = headers.get("Authorization");
        if (!validateDeviceToken(deviceId, token)) {
            log.warn("设备认证失败: deviceId={}, token={}", deviceId, token);
            session.close();
            return;
        }

        // 设置设备 ID 到会话属性
        session.setAttribute("deviceId", deviceId);
        log.info("设备认证成功: deviceId={}", deviceId);
    }

    /**
     * 设备连接建立
     */
    @OnOpen
    public void onOpen(NettySession session, HttpHeaders headers) {
        String deviceId = session.getAttribute("deviceId");

        try {
            // 保存设备会话
            DEVICE_SESSIONS.put(deviceId, session);

            // 创建设备信息
            DeviceInfo deviceInfo = new DeviceInfo();
            deviceInfo.setDeviceId(deviceId);
            deviceInfo.setConnectTime(System.currentTimeMillis());
            deviceInfo.setLastHeartbeat(System.currentTimeMillis());
            DEVICE_INFO_MAP.put(deviceId, deviceInfo);

            log.info("设备连接成功: deviceId={}, 当前在线设备数: {}",
                    deviceId, DEVICE_SESSIONS.size());

            // 发送欢迎消息
            session.sendText("{\"type\":\"welcome\",\"message\":\"设备连接成功\",\"deviceId\":\"" + deviceId + "\"}");

        } catch (Exception e) {
            log.error("设备连接处理异常: deviceId={}", deviceId, e);
        }
    }

    /**
     * 设备断开连接
     */
    @OnClose
    public void onClose(NettySession session) {
        String deviceId = session.getAttribute("deviceId");
        if (StringUtils.hasText(deviceId)) {
            DEVICE_SESSIONS.remove(deviceId);
            DEVICE_INFO_MAP.remove(deviceId);
            log.info("设备断开连接: deviceId={}, 当前在线设备数: {}",
                    deviceId, DEVICE_SESSIONS.size());
        }
    }

    /**
     * 连接异常处理
     */
    @OnError
    public void onError(NettySession session, Throwable throwable) {
        String deviceId = session.getAttribute("deviceId");
        log.error("设备连接异常: deviceId={}", deviceId, throwable);

        if (StringUtils.hasText(deviceId)) {
            DEVICE_SESSIONS.remove(deviceId);
            DEVICE_INFO_MAP.remove(deviceId);
        }
    }

    /**
     * 接收设备消息
     */
    @OnMessage
    public void onMessage(NettySession session, String message) {
        String deviceId = session.getAttribute("deviceId");

        try {
            log.debug("收到设备消息: deviceId={}, message={}", deviceId, message);

            // 处理心跳消息
            if ("heartbeat".equals(message) || "ping".equals(message)) {
                handleHeartbeat(deviceId, session);
                return;
            }

            // 处理设备数据上报
            handleDeviceData(deviceId, message, session);

        } catch (Exception e) {
            log.error("处理设备消息异常: deviceId={}, message={}", deviceId, message, e);
        }
    }

    /**
     * 处理二进制数据
     */
    @OnBinary
    public void onBinary(NettySession session, byte[] bytes) {
        String deviceId = session.getAttribute("deviceId");
        log.debug("收到设备二进制数据: deviceId={}, length={}", deviceId, bytes.length);

        // 处理二进制数据（如图片、文件等）
        handleBinaryData(deviceId, bytes, session);
    }

    /**
     * 处理网络事件（如心跳超时）
     */
    @OnEvent
    public void onEvent(NettySession session, Object evt) {
        String deviceId = session.getAttribute("deviceId");

        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) evt;
            switch (idleEvent.state()) {
                case READER_IDLE:
                    log.warn("设备读取超时: deviceId={}", deviceId);
                    break;
                case WRITER_IDLE:
                    log.warn("设备写入超时: deviceId={}", deviceId);
                    break;
                case ALL_IDLE:
                    log.warn("设备全部超时，断开连接: deviceId={}", deviceId);
                    session.close();
                    break;
            }
        }
    }

    /**
     * 验证设备 token
     */
    private boolean validateDeviceToken(String deviceId, String token) {
        // TODO: 实现真实的设备认证逻辑
        // 这里可以调用认证服务验证设备合法性
        return true;
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(String deviceId, NettySession session) {
        DeviceInfo deviceInfo = DEVICE_INFO_MAP.get(deviceId);
        if (deviceInfo != null) {
            deviceInfo.setLastHeartbeat(System.currentTimeMillis());
            session.sendText("pong");
            log.debug("设备心跳: deviceId={}", deviceId);
        }
    }

    /**
     * 处理设备数据
     */
    private void handleDeviceData(String deviceId, String data, NettySession session) {
        // TODO: 实现设备数据处理逻辑
        // 1. 解析设备数据
        // 2. 存储到数据库
        // 3. 触发业务逻辑
        // 4. 返回响应

        log.info("处理设备数据: deviceId={}, data={}", deviceId, data);

        // 示例：返回确认消息
        String response = "{\"type\":\"ack\",\"status\":\"success\",\"timestamp\":" +
                System.currentTimeMillis() + "}";
        session.sendText(response);
    }

    /**
     * 处理二进制数据
     */
    private void handleBinaryData(String deviceId, byte[] data, NettySession session) {
        // TODO: 实现二进制数据处理逻辑
        log.info("处理设备二进制数据: deviceId={}, size={} bytes", deviceId, data.length);

        // 示例：返回确认
        session.sendBinary(new byte[] { 0x01 }); // 确认收到
    }

    /**
     * 向指定设备发送消息
     */
    public boolean sendToDevice(String deviceId, String message) {
        NettySession session = DEVICE_SESSIONS.get(deviceId);
        log.info("向设备发送消息: deviceId={}, message={}", deviceId, message);
        if (session != null && session.isOpen()) {
            session.sendText(message);
            log.info("消息发送成功: deviceId={}, message={}", deviceId, message);
            return true;
        }
        log.warn("设备不在线或发送失败: deviceId={}, message={}", deviceId, message);
        return false;
    }

    /**
     * 向所有设备广播消息
     */
    public void broadcastToAllDevices(String message) {
        DEVICE_SESSIONS.forEach((deviceId, session) -> {
            if (session.isOpen()) {
                session.sendText(message);
            }
        });
    }

    /**
     * 获取在线设备数量
     */
    public int getOnlineDeviceCount() {
        return DEVICE_SESSIONS.size();
    }

    /**
     * 获取所有在线设备 ID
     */
    public java.util.Set<String> getOnlineDeviceIds() {
        return DEVICE_SESSIONS.keySet();
    }

    /**
     * 设备信息类
     */
    public static class DeviceInfo {
        private String deviceId;
        private long connectTime;
        private long lastHeartbeat;

        // getter/setter 方法
        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public long getConnectTime() {
            return connectTime;
        }

        public void setConnectTime(long connectTime) {
            this.connectTime = connectTime;
        }

        public long getLastHeartbeat() {
            return lastHeartbeat;
        }

        public void setLastHeartbeat(long lastHeartbeat) {
            this.lastHeartbeat = lastHeartbeat;
        }
    }
}