package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;
import com.hihonor.iot.ws.connection.model.SetProgramTaskData;
import com.hihonor.iot.ws.connection.model.ProgramInfo;

import cn.twelvet.websocket.netty.domain.NettySession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 设置程序任务消息处理器
 * 处理平台向设备下发的程序任务设置消息
 * 注意：这个处理器主要用于验证和记录，实际的SET_PROGRAM_TASK消息通常是平台主动发送给设备的
 */
@Component
public class SetProgramTaskMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(SetProgramTaskMessageHandler.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public BaseWebSocketMessage<?> handle(NettySession session, String resourceid, 
            BaseWebSocketMessage<ObjectNode> processedMessage) throws JsonProcessingException {
        
        log.info("收到来自 resourceid={} 的程序任务设置消息。Timestamp: {}", resourceid, processedMessage.getTimestamp());
        
        // 解析程序任务设置数据
        ObjectNode dataNode = processedMessage.getData();
        if (dataNode == null || dataNode.isNull()) {
            log.warn("设备 {} 发送的程序任务设置消息，但 data 字段为空", resourceid);
            return createErrorResponse(processedMessage.getMessageId(), "数据字段为空");
        }
        
        SetProgramTaskData taskData = objectMapper.treeToValue(dataNode, SetProgramTaskData.class);
        
        // 验证必要字段
        if (taskData.getResourceid() == null || taskData.getTaskOrder() == null || 
            taskData.getPrograms() == null || taskData.getPrograms().isEmpty()) {
            log.warn("设备 {} 发送的程序任务设置数据不完整: {}", resourceid, taskData);
            return createErrorResponse(processedMessage.getMessageId(), "数据不完整");
        }
        
        // 验证resourceid是否匹配
        if (!resourceid.equals(taskData.getResourceid())) {
            log.warn("设备 {} 发送的程序任务设置中resourceid不匹配: 期望={}, 实际={}", 
                resourceid, resourceid, taskData.getResourceid());
            return createErrorResponse(processedMessage.getMessageId(), "设备ID不匹配");
        }
        
        // 处理程序任务设置
        boolean success = processTaskSetting(resourceid, taskData);
        
        // 创建响应
        Map<String, Object> responseData;
        String responseType;
        
        if (success) {
            responseData = Map.of(
                "status", "accepted",
                "taskOrder", taskData.getTaskOrder(),
                "programCount", taskData.getPrograms().size(),
                "message", "程序任务设置已接受"
            );
            responseType = "SET_PROGRAM_TASK_ACK";
            log.info("向 resourceid={} 发送程序任务设置确认，任务令: {}", resourceid, taskData.getTaskOrder());
        } else {
            responseData = Map.of(
                "status", "rejected",
                "taskOrder", taskData.getTaskOrder(),
                "message", "程序任务设置被拒绝"
            );
            responseType = "SET_PROGRAM_TASK_REJECT";
            log.warn("向 resourceid={} 发送程序任务设置拒绝，任务令: {}", resourceid, taskData.getTaskOrder());
        }
        
        BaseWebSocketMessage<Map<String, Object>> response = new BaseWebSocketMessage<>(
            responseType,
            null, // Timestamp会由发送方法自动填充
            processedMessage.getMessageId(), // 回传原始messageId
            responseData
        );
        
        return response;
    }
    
    /**
     * 处理程序任务设置的业务逻辑
     */
    private boolean processTaskSetting(String resourceid, SetProgramTaskData taskData) {
        log.info("处理设备 {} 的程序任务设置，任务令: {}", resourceid, taskData.getTaskOrder());
        
        try {
            // 验证程序信息
            for (ProgramInfo program : taskData.getPrograms()) {
                log.info("设置轨道 {} 程序: {}", program.getTrackId(), program.getProgramName());
                
                // 验证轨道ID
                if (program.getTrackId() == null || program.getTrackId().trim().isEmpty()) {
                    log.error("轨道ID为空: {}", program);
                    return false;
                }
                
                // 验证程序名称
                if (program.getProgramName() == null || program.getProgramName().trim().isEmpty()) {
                    log.error("程序名称为空: {}", program);
                    return false;
                }
                
                // 这里可以添加更多验证逻辑，例如：
                // 1. 检查程序是否存在
                // 2. 验证轨道是否可用
                // 3. 检查设备状态是否允许设置新任务
                // 4. 验证任务优先级等
            }
            
            // 这里可以添加具体的业务处理逻辑，例如：
            // 1. 保存任务到数据库
            // 2. 更新设备状态
            // 3. 发送通知给相关系统
            // 4. 启动任务执行流程
            
            log.info("程序任务设置处理成功 - 设备: {}, 任务令: {}, 程序数量: {}", 
                resourceid, taskData.getTaskOrder(), taskData.getPrograms().size());
            
            return true;
            
        } catch (Exception e) {
            log.error("处理程序任务设置时发生错误 - 设备: {}, 任务令: {}", 
                resourceid, taskData.getTaskOrder(), e);
            return false;
        }
    }
    
    /**
     * 创建错误响应
     */
    private BaseWebSocketMessage<Map<String, Object>> createErrorResponse(String messageId, String errorMessage) {
        Map<String, Object> errorData = Map.of(
            "status", "error",
            "error", errorMessage,
            "timestamp", System.currentTimeMillis()
        );
        
        return new BaseWebSocketMessage<>(
            "SET_PROGRAM_TASK_ERROR",
            null,
            messageId,
            errorData
        );
    }
    
    @Override
    public String getMessageTypeHandled() {
        return "SET_PROGRAM_TASK";
    }
}
