package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;

import cn.twelvet.websocket.netty.domain.NettySession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 默认消息处理器
 * 处理未知类型的消息，提供基本的响应
 */
@Component
public class DefaultMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultMessageHandler.class);
    
    @Override
    public BaseWebSocketMessage<?> handle(NettySession session, String resourceid, 
            BaseWebSocketMessage<ObjectNode> processedMessage) throws JsonProcessingException {
        
        String messageType = processedMessage.getType();
        log.warn("收到来自 resourceid={} 的未知消息类型: {}, 使用默认处理器处理", resourceid, messageType);
        
        // 记录消息内容用于调试
        log.debug("未知消息内容 - resourceid={}, type={}, messageId={}, data={}", 
            resourceid, messageType, processedMessage.getMessageId(), processedMessage.getData());
        
        // 创建通用响应
        Map<String, Object> responseData = Map.of(
            "status", "unknown_message_type",
            "originalType", messageType != null ? messageType : "null",
            "message", "收到未知类型的消息，已记录但未处理",
            "timestamp", System.currentTimeMillis()
        );
        
        BaseWebSocketMessage<Map<String, Object>> response = new BaseWebSocketMessage<>(
            "UNKNOWN_MESSAGE_ACK",
            null, // Timestamp会由发送方法自动填充
            processedMessage.getMessageId(), // 回传原始messageId
            responseData
        );
        
        log.info("向 resourceid={} 发送未知消息类型确认", resourceid);
        return response;
    }
    
    @Override
    public String getMessageTypeHandled() {
        // 默认处理器不处理特定类型，返回特殊标识
        return "DEFAULT";
    }
}
