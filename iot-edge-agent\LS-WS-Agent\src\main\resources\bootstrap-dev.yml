#spring:
#  application:
#    name: plc-agent
logging:
  config:  classpath:logback-spring.xml
#  profiles:
#    active: test
#  cloud:
#    nacos:
#      config:
#        server-addr: mse.beta.hihonor.com:80
#        context-path: /nacos/service

# 测试环境使用pekctxt
docker_region: pekctxt
# 应用配置环境
docker_env: sit1
# 应用信息
application:
  # IAM项目ID，应用ID
  appId: 9ad5727dad4c4a55b8f16e2dd0500420
  # 部署单元
  subAppId: plc-cachingMachine-agent
# IAM项目信息
#iam:
#  enterprise: 99999999999999999999999999999999
#  project: 9ad5727dad4c4a55b8f16e2dd0500420
#  endpoint: http://apig.heds.hihonor.com/api
#  account: 9ad5727dad4c4a55b8f16e2dd0500420
#  secret: FO5tQnLxgt3CKC0mTYDc0J8CAn56Z+c/oqeFd3cC

spring:
  application:
    name: plc-agent
  profiles:
    active: test
  cloud:
    nacos:
      config:
        endpoint: mse.beta.hihonor.com:80
        context-path: /nacos-address/service/nacos
    compatibility-verifier:
      enabled: false
#        server-addr: mse.beta.hihonor.com:80
#        context-path: /nacos/service
truss:
  apimall:
    enterprise: 99999999999999999999999999999999
    ## 生产为：https://yun.hihonor.com
    endpoint: https://yun.beta.hihonor.com
    ## apimall中注册的API集成账号
    project: 9ad5727dad4c4a55b8f16e2dd0500420
    account: 9ad5727dad4c4a55b8f16e2dd0500420
    secret: FO5tQnLxgt3CKC0mTYDc0J8CAn56Z+c/oqeFd3cC
