package com.hihonor.iot.ws.connection.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用 WebSocket 消息基础结构。
 * 根据 IOT 平台设备接入规范 (WebSocket) 定义。
 *
 * @param <T> data字段的具体类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // 序列化时忽略null字段
public class BaseWebSocketMessage<T> {

    /**
     * 消息的类型，用于区分不同的业务操作。
     * (String, Mandatory)
     */
    private String type;

    /**
     * 消息发送时的UTC标准时间戳，使用毫秒级Unix时间戳（自1970年1月1日00:00:00 UTC起的毫秒数）。
     * (Number, Optional)
     * 使用数字类型可以屏蔽时区概念，确保全球统一的时间标准。
     * 如果设备端未提供此字段，IOT平台将在接收消息或生成消息时填充当前UTC时间戳。
     */
    private Long timestamp;

    /**
     * 唯一消息ID，建议设备端和平台端对需要可靠对应的请求/响应消息使用此字段。
     * (String, Optional)
     */
    private String messageId;

    /**
     * 包含该消息类型特定的业务数据对象。
     * (Object, Mandatory in general, but can be null for specific types like
     * HEARTBEAT if data field is omitted)
     * 规范中对HEARTBEAT的描述指明data字段本身是可选的，这与通用结构data是Mandatory有出入。
     * 此处允许为null以兼容HEARTBEAT可能不带data字段的情况，或data字段存在但其内容为空对象。
     */
    private T data;
}