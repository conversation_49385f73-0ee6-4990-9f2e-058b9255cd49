package com.hihonor.iot.ws.connection.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CONNECTION_ACK 消息的 data 字段结构。
 * 平台连接成功后发送给设备。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionAckData {

    /**
     * 连接状态。
     * (String, Mandatory, e.g., "success")
     */
    private String status;

    /**
     * 连接设备的resourceid。
     * (String, Mandatory)
     */
    private String resourceid;

    /**
     * 描述信息。
     * (String, Mandatory, e.g., "设备连接成功")
     */
    private String message;
}