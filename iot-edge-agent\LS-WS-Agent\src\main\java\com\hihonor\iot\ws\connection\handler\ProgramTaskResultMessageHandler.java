package com.hihonor.iot.ws.connection.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;
import com.hihonor.iot.ws.connection.model.ProgramTaskResultData;
import com.hihonor.iot.ws.connection.model.ProgramResult;

import cn.twelvet.websocket.netty.domain.NettySession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 程序任务结果消息处理器
 * 处理设备上报的程序执行结果
 */
@Component
public class ProgramTaskResultMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(ProgramTaskResultMessageHandler.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public BaseWebSocketMessage<?> handle(NettySession session, String resourceid, 
            BaseWebSocketMessage<ObjectNode> processedMessage) throws JsonProcessingException {
        
        log.info("收到来自 resourceid={} 的程序任务结果消息。Timestamp: {}", resourceid, processedMessage.getTimestamp());
        
        // 解析程序任务结果数据
        ObjectNode dataNode = processedMessage.getData();
        if (dataNode == null || dataNode.isNull()) {
            log.warn("设备 {} 发送的程序任务结果消息，但 data 字段为空", resourceid);
            return createErrorResponse(processedMessage.getMessageId(), "数据字段为空");
        }
        
        ProgramTaskResultData resultData = objectMapper.treeToValue(dataNode, ProgramTaskResultData.class);
        
        // 验证必要字段
        if (resultData.getResourceid() == null || resultData.getTaskOrder() == null || 
            resultData.getPrograms() == null || resultData.getPrograms().isEmpty()) {
            log.warn("设备 {} 发送的程序任务结果数据不完整: {}", resourceid, resultData);
            return createErrorResponse(processedMessage.getMessageId(), "数据不完整");
        }
        
        // 验证resourceid是否匹配
        if (!resourceid.equals(resultData.getResourceid())) {
            log.warn("设备 {} 发送的程序任务结果中resourceid不匹配: 期望={}, 实际={}", 
                resourceid, resourceid, resultData.getResourceid());
            return createErrorResponse(processedMessage.getMessageId(), "设备ID不匹配");
        }
        
        // 处理程序执行结果
        processTaskResults(resourceid, resultData);
        
        // 创建确认响应
        Map<String, Object> ackData = Map.of(
            "status", "received",
            "taskOrder", resultData.getTaskOrder(),
            "processedPrograms", resultData.getPrograms().size(),
            "message", "程序任务结果已接收并处理"
        );
        
        BaseWebSocketMessage<Map<String, Object>> resultAck = new BaseWebSocketMessage<>(
            "PROGRAM_TASK_RESULT_ACK",
            null, // Timestamp会由发送方法自动填充
            processedMessage.getMessageId(), // 回传原始messageId
            ackData
        );
        
        log.info("向 resourceid={} 发送程序任务结果确认，任务令: {}", resourceid, resultData.getTaskOrder());
        return resultAck;
    }
    
    /**
     * 处理程序执行结果的业务逻辑
     */
    private void processTaskResults(String resourceid, ProgramTaskResultData resultData) {
        log.info("处理设备 {} 的程序任务结果，任务令: {}", resourceid, resultData.getTaskOrder());
        
        for (ProgramResult program : resultData.getPrograms()) {
            log.info("轨道 {} 程序 {} 执行结果: {} - {}", 
                program.getTrackId(), 
                program.getProgramName(), 
                program.isSuccess() ? "成功" : "失败",
                program.getResultMessage());
            
            // 这里可以添加具体的业务处理逻辑，例如：
            // 1. 更新数据库中的任务状态
            // 2. 发送通知给上游系统
            // 3. 记录执行日志
            // 4. 触发后续流程
            
            if (!program.isSuccess()) {
                log.warn("程序执行失败 - 设备: {}, 轨道: {}, 程序: {}, 错误: {}", 
                    resourceid, program.getTrackId(), program.getProgramName(), program.getResultMessage());
                // 可以在这里添加失败处理逻辑
            }
        }
        
        // TODO: 实现具体的业务逻辑
        // - 数据库操作
        // - 消息队列发送
        // - 状态更新
        // - 告警处理等
    }
    
    /**
     * 创建错误响应
     */
    private BaseWebSocketMessage<Map<String, Object>> createErrorResponse(String messageId, String errorMessage) {
        Map<String, Object> errorData = Map.of(
            "status", "error",
            "error", errorMessage,
            "timestamp", System.currentTimeMillis()
        );
        
        return new BaseWebSocketMessage<>(
            "PROGRAM_TASK_RESULT_ERROR",
            null,
            messageId,
            errorData
        );
    }
    
    @Override
    public String getMessageTypeHandled() {
        return "PROGRAM_TASK_RESULT";
    }
}
