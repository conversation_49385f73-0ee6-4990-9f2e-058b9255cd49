# IOT 平台设备接入规范 (WebSocket)

## 1. 引言

### 1.1. 文档目的

本文档旨在定义设备（如镭雕机等）通过 WebSocket 协议接入 IOT 平台的通讯规范。详细描述了连接管理、消息格式、接口定义以及关键业务交互流程，旨在为设备端开发人员提供清晰的对接指南，确保设备与平台之间稳定、高效的数据交换。

### 1.2. 适用范围

本规范适用于所有需要通过 WebSocket 协议与 IOT 平台进行实时双向通讯的设备，特别是那些需要接收平台指令并上报执行状态的场景，例如镭雕加工、自动化控制等。

## 2. 通讯协议

### 2.1. 基础协议

设备与 IOT 平台之间采用 **WebSocket** (WS) 协议进行实时双向通讯

### 2.2. 连接端点

设备应连接到以下服务端点：

- **生产环境**: `ws://<IOT_PLATFORM_DOMAIN>/ws/connect` (具体域名由 IOT 平台方提供)
- **测试环境**: (如果提供，具体域名由 IOT 平台方提供)

`<IOT_PLATFORM_DOMAIN>` 是 IOT 平台的域名或 IP 地址及端口。

### 2.3. 数据格式

所有通过 WebSocket 传输的业务数据均采用 **JSON** (JavaScript Object Notation) 格式进行编码和解码。
字符集统一使用 **UTF-8**。

## 3. 连接管理

### 3.1. 连接握手与认证

设备在与 IOT 平台建立 WebSocket 连接时，需要提供其唯一的设备标识。

1.  **设备标识 (`resourceid`) - HTTP 请求头**:
    设备在发起 WebSocket 连接的初始 HTTP 握手请求时，必须通过自定义请求头 `X-resourceid` 传递其唯一的设备资产编码（对应业务需求文档中的"设备 ID"）。

    - 请求头示例: `X-resourceid: A2109003335`

    **服务器处理逻辑**: IOT 平台服务器会从 HTTP 请求头 `X-resourceid` 读取设备 ID。如果请求头中不存在该字段或其值为空，连接将被拒绝。

2.  **连接唯一性**: IOT 平台只允许具有相同 `resourceid` 的设备保持一个活动的 WebSocket 连接。如果一个已连接的 `resourceid` 再次尝试连接，新的连接请求将被拒绝

### 3.2. 连接建立响应

成功建立 WebSocket 连接后，IOT 平台会立即向设备发送一条连接确认消息。

- **消息类型**: `CONNECTION_ACK`
- **示例**:

  ```json
  {
    "type": "CONNECTION_ACK",
    "timestamp": 1717398000000,
    "data": {
      "status": "success",
      "resourceid": "A2109003335",
      "message": "设备连接成功"
    }
  }
  ```

### 3.3. 心跳机制

为保持连接的活性并及时检测断连，设备与平台需遵循以下心跳机制：

1.  **设备端发送心跳**: 设备应**每隔 30 秒**主动向 IOT 平台发送一次心跳消息。如果设备在该周期内有其他业务数据上报，则可以不单独发送心跳消息，任何上行消息均可视为活动信号。
2.  **平台端超时检测**: IOT 平台会监测设备的活动状态。如果平台在**90 秒**内未收到来自设备的任何消息（包括心跳消息或其他业务消息），平台将认为该设备连接超时，并会主动断开该 WebSocket 连接。

### 3.4. 连接断开

连接可能因以下原因断开：

- 设备主动关闭连接。
- 平台主动关闭连接（如：心跳超时、认证失败、相同`resourceid`重复连接等）。
- 网络故障。

设备应具备断线重连机制，在连接意外断开后尝试重新建立连接。

## 4. 消息接口规范

所有业务相关的消息均采用统一的 JSON 结构，包含 `type`, `timestamp`, `messageId` (可选), 和 `data` 字段。

```json
{
  "type": "MESSAGE_TYPE_STRING",
  "timestamp": 1717398000000,
  "messageId": "optional-unique-message-id",
  "data": {
    // ... 具体的业务数据 ...
  }
}
```

### 4.1. 通用字段说明

- `type` (String, Mandatory): 消息的类型，用于区分不同的业务操作。见后续具体定义。
- `timestamp` (Number, Optional): 消息发送时的UTC标准时间戳，使用毫秒级Unix时间戳（自1970年1月1日00:00:00 UTC起的毫秒数）。使用数字类型可以屏蔽时区概念，确保全球统一的时间标准。如果设备端未提供此字段，IOT 平台将在接收消息或生成消息时填充当前UTC时间戳。
- `messageId` (String, Optional): 唯一消息 ID，建议设备端和平台端对需要可靠对应的请求/响应消息使用此字段。
- `data` (Object, Mandatory): 包含该消息类型特定的业务数据对象。

### 4.2. 下行消息 (IOT Platform -> 设备)

#### 4.2.1. 设置程序与任务 (`SET_PROGRAM_TASK`)

- **用途**: IOT 平台向设备下发程序名称、生产任务令等加工信息。
- **`type`**: `"SET_PROGRAM_TASK"`
- **`data` 对象字段**:
  - `resourceid` (String, Mandatory): 目标设备的 `resourceid` (设备资产编码)。
  - `taskOrder` (String, Mandatory): 生产任务令，例如：`"RTZ6H55K16V"`。
  - `programs` (Array of Objects, Mandatory): 程序列表，包含一个或两个轨道的程序信息。
    - `trackId` (String, Mandatory): 轨道号，例如：`"1"` (一轨), `"2"` (二轨)。
    - `programName` (String, Mandatory): 程序名称，可能包含中文。例如：`"HN2DNNSU001-C-Pass"` 或 `"HN2DNNSU001-C-T"`。
- **示例**:
  ```json
  {
    "type": "SET_PROGRAM_TASK",
    "timestamp": 1717398060000,
    "messageId": "iot-req-001",
    "data": {
      "resourceid": "A2109003335",
      "taskOrder": "RTZ6H55K16V",
      "programs": [
        {
          "trackId": "1",
          "programName": "HN2DNNSU001-C-Pass"
        },
        {
          "trackId": "2",
          "programName": "HN2DNNSU001-C-T"
        }
      ]
    }
  }
  ```

### 4.3. 上行消息 (设备 -> IOT Platform)

#### 4.3.1. 程序与任务执行结果 (`PROGRAM_TASK_RESULT`)

- **用途**: 设备向 IOT 平台上报程序与任务的执行状态/结果。
- **`type`**: `"PROGRAM_TASK_RESULT"`
- **`data` 对象字段**:
  - `resourceid` (String, Mandatory): 来源设备的 `resourceid`。
  - `taskOrder` (String, Mandatory): 对应的生产任务令。
  - `programs` (Array of Objects, Mandatory): 对应程序的执行结果列表。
    - `trackId` (String, Mandatory): 轨道号。
    - `programName` (String, Mandatory): 程序名称。
    - `success` (Boolean, Mandatory): 操作是否成功。`true` 表示成功，`false` 表示失败。
    - `resultMessage` (String, Optional): 执行结果的详细描述信息。对于失败情况，应包含错误原因。
- **示例**:
  ```json
  {
    "type": "PROGRAM_TASK_RESULT",
    "timestamp": 1717398120000,
    "messageId": "device-resp-001",
    "data": {
      "resourceid": "A2109003335",
      "taskOrder": "RTZ6H55K16V",
      "programs": [
        {
          "trackId": "1",
          "programName": "HN2DNNSU001-C-Pass",
          "success": true,
          "resultMessage": "一轨加工完成"
        },
        {
          "trackId": "2",
          "programName": "HN2DNNSU001-C-T",
          "success": false,
          "resultMessage": "二轨有料，但加工失败，错误码XXX"
        }
      ]
    }
  }
  ```

#### 4.3.2. 心跳消息 (`HEARTBEAT`)

- **用途**: 设备发送心跳以维持与服务器的连接，并表明自身处于活动状态。
- **`type`**: `"HEARTBEAT"`
- **`data`（可选） 对象字段**: (可以为空对象，或包含设备状态摘要信息)
  - `status` (String, Optional): 设备当前状态简述，如 `"IDLE"`, `"RUNNING"`。
- **示例**:
  ```json
  {
    "type": "HEARTBEAT",
    "timestamp": 1717398300000,
    "data": {
      "status": "IDLE"
    }
  }
  ```
  或者一个更简单的心跳，仅包含必须字段：
  ```json
  {
    "type": "HEARTBEAT",
    "timestamp": 1717398300000
  }
  ```

## 5. 交互流程示例

1.  **设备连接与上线**:

    - 设备使用 `ws://<IOT_PLATFORM_DOMAIN>/ws/connect` 发起 WebSocket 连接。
    - 设备在连接时，需要通过自定义请求头 `X-resourceid` 传递其唯一的设备资产编码（对应业务需求文档中的"设备 ID"）。
    - IOT 平台验证 `resourceid`，若通过，则连接建立。
    - IOT 平台向设备发送 `CONNECTION_ACK` 消息。
    - 设备开始按 3.3 节定义的规则发送 `HEARTBEAT` 消息。

2.  **IOT 平台下发任务**:

    - IOT 平台向指定设备发送 `SET_PROGRAM_TASK` 消息，包含任务令、程序名称等。

3.  **设备执行并上报结果**:
    - 设备收到 `SET_PROGRAM_TASK` 消息后，开始执行。
    - 执行完成后（或过程中关键节点），设备向 IOT 平台发送 `PROGRAM_TASK_RESULT` 消息，上报执行结果。
4.  **持续心跳**:

    - 在无业务消息交互期间，设备持续发送 `HEARTBEAT` 消息。
    - IOT 平台根据收到的消息（业务或心跳）更新设备的最后活动时间。

5.  **连接超时断开**:
    - 若设备在 90 秒内未发送任何消息，IOT 平台主动断开连接。
    - 设备应尝试重连。

## 6. 错误处理与超时

### 6.1. 连接级别错误

- **认证失败**: 如果 `resourceid` 无效或不符合平台策略，连接将被拒绝或立即关闭。
- **网络问题**: 网络波动可能导致连接中断。设备应实现重连逻辑。

### 6.2. 消息级别错误

- **格式错误**: 如果某一方收到的 JSON 消息格式不正确（如：缺少必要字段、数据类型错误），应记录错误日志，并可选择性地向对方发送一个错误提示消息（需预定义错误消息类型）。
- **业务逻辑错误**: IOT 平台不参与业务逻辑，只负责通讯。

### 6.3. 业务超时

- **设备响应超时 (5 分钟)**: "5 分钟内，镭雕机必须返回程序名称（应理解为对 `SET_PROGRAM_TASK` 的业务响应，即 `PROGRAM_TASK_RESULT`），如果调 IOT 调用设备 超过 5 分钟 ，IOT 则会直接返回上游设备设备通讯失败。"
  - 如果在此时间内未收到设备针对该任务的有效 `PROGRAM_TASK_RESULT` ，IOT 平台将认为该业务操作超时，并向上游报告失败。
  - **注意**: 此业务超时独立于 3.3 节中定义的 90 秒连接心跳超时。连接可能仍然存在，但业务操作被判定为超时失败。

## 7. 文档版本

| 版本 | 日期       | 修订者 | 说明                       |
| ---- | ---------- | ------ | -------------------------- |
| V1.0 | 2025-06-03 | 周光印 | 初稿创建，基于业务需求定义 |
| V1.1 | 2025-01-27 | 周光印 | 将时间戳改为UTC标准时间戳（数字类型），屏蔽时区概念 |

---
