/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.ws.ls;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@SpringBootApplication
@ComponentScan(value = { "com.hihonor.iot.edge.*", "com.hihonor.iot.ws.*" })
@EnableScheduling
@EnableConfigurationProperties
public class LsApp {

    public static void main(String[] args) {
        SpringApplication.run(LsApp.class);
        log.info("WebSocket IoT Server Application start success");
    }
}
