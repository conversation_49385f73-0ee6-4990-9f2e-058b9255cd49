/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.ws.connection;

import java.util.Map;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;
import com.hihonor.iot.ws.connection.model.ConnectionAckData;
import com.hihonor.iot.ws.connection.model.SetProgramTaskData;
import com.hihonor.iot.ws.connection.model.ProgramInfo;
import com.hihonor.iot.ws.connection.handler.MessageHandlerManager;

import cn.twelvet.websocket.netty.annotation.BeforeHandshake;
import cn.twelvet.websocket.netty.annotation.OnBinary;
import cn.twelvet.websocket.netty.annotation.OnClose;
import cn.twelvet.websocket.netty.annotation.OnError;
import cn.twelvet.websocket.netty.annotation.OnEvent;
import cn.twelvet.websocket.netty.annotation.OnMessage;
import cn.twelvet.websocket.netty.annotation.OnOpen;
import cn.twelvet.websocket.netty.annotation.RequestParam;
import cn.twelvet.websocket.netty.annotation.WebSocketEndpoint;
import cn.twelvet.websocket.netty.domain.NettySession;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.timeout.IdleStateEvent;




/**
 * 设备 WebSocket 服务器端点
 * 负责处理设备连接、消息和生命周期事件。
 * 
 * <AUTHOR>
 * @since 2025-06-03
 */
@WebSocketEndpoint(path = "/ws/connect", port = "${netty.websocket.port}")
@Component
public class DeviceWebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(DeviceWebSocketServer.class);


    @Autowired
    private DeviceSessionManager deviceSessionManager;

    @Autowired
    private MessageHandlerManager messageHandlerManager;

    private final ObjectMapper objectMapper;

    @Autowired
    public DeviceWebSocketServer(DeviceSessionManager deviceSessionManager, ObjectMapper objectMapper) {
        this.deviceSessionManager = deviceSessionManager;
        this.objectMapper = objectMapper;
        // ObjectMapper 已配置为处理UTC时间戳（数字类型）
        log.info("DeviceWebSocketServer: 构造函数被调用。DeviceSessionManager 和 ObjectMapper 已注入。");
    }

    /**
     * 生成当前UTC时间戳（毫秒级Unix时间戳）。
     */
    private Long getCurrentUtcTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 握手前进行设备认证和resourceid检查。
     */
    @BeforeHandshake
    public void beforeHandshake(NettySession session, HttpHeaders headers,
            @RequestParam(name = "resourceid", required = false) String queryResourceid) {
        String resourceid = extractResourceid(headers, queryResourceid);

        if (!StringUtils.hasText(resourceid)) {
            log.warn("握手失败：连接请求中缺少有效的 resourceid (请求头 X-resourceid 或 查询参数 resourceid)。");
            session.close();
            return;
        }

        if (!deviceSessionManager.tryRegisterSession(session, resourceid)) {
            session.close();
            return;
        }
        session.setAttribute("resourceid", resourceid);
        log.info("握手成功，设备认证通过: resourceid={}", resourceid);
    }

    /**
     * 设备连接成功建立。
     */
    @OnOpen
    public void onOpen(NettySession session, HttpHeaders headers) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.error("onOpen错误：无法从session获取resourceid，可能在beforeHandshake阶段未成功设置。关闭连接。");
            session.close();
            return;
        }
        log.info("设备连接成功打开: resourceid={}, 当前会话ID: {}, 当前在线设备数: {}",
                resourceid, session.id(), deviceSessionManager.getOnlineDeviceCount());

        ConnectionAckData ackData = new ConnectionAckData("success", resourceid, "设备连接成功");
        BaseWebSocketMessage<ConnectionAckData> ackMessage = new BaseWebSocketMessage<>(
                "CONNECTION_ACK",
                null, // Timestamp会由sendMessageToDevice自动填充
                null, // messageId 可选
                ackData);
        sendMessageToDevice(resourceid, ackMessage);
    }

    /**
     * 设备连接关闭。
     */
    @OnClose
    public void onClose(NettySession session) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (StringUtils.hasText(resourceid)) {
            deviceSessionManager.unregisterSession(session);
            log.info("设备连接已关闭: resourceid={}", resourceid);
        } else {
            log.info("一个未成功认证的会话已关闭: 会话ID={}", session.id());
            deviceSessionManager.unregisterSession(session);
        }
    }

    /**
     * 设备连接发生错误。
     */
    @OnError
    public void onError(NettySession session, Throwable throwable) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (StringUtils.hasText(resourceid)) {
            log.error("设备连接发生错误: resourceid={}", resourceid, throwable);
            deviceSessionManager.unregisterSession(session);
        } else {
            log.error("一个未成功认证的会话发生错误: 会话ID={}", session.id(), throwable);
            deviceSessionManager.unregisterSession(session);
        }
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * 收到来自设备的文本消息（预期为JSON）。
     */
    @OnMessage
    public void onMessage(NettySession session, String message) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.warn("收到来自未认证会话的消息，已忽略。会话ID: {}", session.id());
            session.close();
            return;
        }

        deviceSessionManager.updateLastMessageTime(resourceid);
        log.debug("收到原始设备消息: resourceid={}, message={}", resourceid, message);

        try {
            // 1. 初步反序列化为 BaseWebSocketMessage 以获取 type 和 data (作为 ObjectNode)
            JavaType baseMessageType = objectMapper.getTypeFactory().constructParametricType(BaseWebSocketMessage.class,
                    ObjectNode.class);
            BaseWebSocketMessage<ObjectNode> baseMessage = objectMapper.readValue(message, baseMessageType);

            // 2. 处理 timestamp (如果设备未提供，平台生成)
            if (baseMessage.getTimestamp() == null) {
                baseMessage.setTimestamp(getCurrentUtcTimestamp());
                log.debug("设备 {} 未提供timestamp，平台已生成: {}", resourceid, baseMessage.getTimestamp());
            }

            // 3. 使用消息处理器管理器处理消息
            BaseWebSocketMessage<?> response = messageHandlerManager.handleMessage(session, resourceid, baseMessage);

            // 4. 如果有响应消息，发送给设备
            if (response != null) {
                sendMessageToDevice(resourceid, response);
            }

        } catch (JsonProcessingException e) {
            log.error("反序列化设备消息失败: resourceid={}, message={}", resourceid, message, e);
            // 发送错误响应
            sendErrorResponse(resourceid, null, "消息格式错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理设备消息时发生意外错误: resourceid={}, message={}", resourceid, message, e);
            // 发送错误响应
            sendErrorResponse(resourceid, null, "消息处理错误: " + e.getMessage());
        }
    }

    /**
     * 发送错误响应消息
     *
     * @param resourceid 设备ID
     * @param messageId 原始消息ID
     * @param errorMessage 错误信息
     */
    private void sendErrorResponse(String resourceid, String messageId, String errorMessage) {
        Map<String, Object> errorData = Map.of(
            "status", "error",
            "error", errorMessage,
            "timestamp", System.currentTimeMillis()
        );

        BaseWebSocketMessage<Map<String, Object>> errorResponse = new BaseWebSocketMessage<>(
            "MESSAGE_ERROR",
            null, // Timestamp会由sendMessageToDevice自动填充
            messageId,
            errorData
        );

        sendMessageToDevice(resourceid, errorResponse);
    }

    /**
     * 收到来自设备的二进制消息。
     */
    @OnBinary
    public void onBinary(NettySession session, byte[] bytes) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.warn("收到来自未认证会话的二进制消息，已忽略。会话ID: {}", session.id());
            session.close();
            return;
        }
        deviceSessionManager.updateLastMessageTime(resourceid);
        log.debug("收到设备二进制数据: resourceid={}, length={}", resourceid, bytes.length);
    }

    /**
     * 处理Netty事件，主要用于心跳超时检测。
     */
    @OnEvent
    public void onEvent(NettySession session, Object evt) {
        // 保留此日志以捕获所有到达的事件，但不再特别处理IdleStateEvent.READER_IDLE_STATE_EVENT
        log.info("DeviceWebSocketServer @OnEvent: 收到事件类型: {}, 会话ID: {}", evt.getClass().getName(), session.id());

        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            // 对于未成功认证的会话，如果触发了IdleStateEvent，仍然可以记录（但不会处理超时）
            if (evt instanceof IdleStateEvent) {
                log.info("未认证会话 {} 触发IdleStateEvent: {}", session.id(), ((IdleStateEvent) evt).state());
            }
            return;
        }

        // 移除了原先处理 IdleStateEvent.READER_IDLE_STATE_EVENT 的逻辑，
        // 因为超时管理现在由 ConnectionTimeoutTask 负责。
        // 可以根据需要在这里处理其他类型的Netty事件。
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) evt;
            // 记录其他可能的空闲事件，但不做处理
            if (idleEvent.state() != IdleStateEvent.READER_IDLE_STATE_EVENT.state()) {
                log.debug("设备 resourceid={} 触发其他空闲事件: {}", resourceid, idleEvent.state());
            }
        }
    }

    /**
     * 从HTTP头中提取resourceid。
     * 根据最新规范，resourceid必须通过 "X-resourceid" 请求头传递。
     */
    private String extractResourceid(HttpHeaders headers,
            @RequestParam(name = "resourceid", required = false) String queryResourceid) {
        // 根据最新规范，queryResourceid 参数不再使用，但注解保留以避免潜在的框架方法签名匹配问题。
        // 实际逻辑只检查请求头。
        String headerResourceid = headers.get("X-resourceid");
        if (StringUtils.hasText(headerResourceid)) {
            return headerResourceid;
        }
        // 如果请求头中没有 X-resourceid，则返回 null，这将导致握手失败。
        return null;
    }

    /**
     * 向指定设备发送经过包装的 WebSocket 消息对象。
     *
     * @param resourceid    目标设备的resourceid
     * @param messageObject 要发送的 BaseWebSocketMessage 对象
     * @return 如果发送成功返回true，否则返回false
     */
    public boolean sendMessageToDevice(String resourceid, BaseWebSocketMessage<?> messageObject) {
        NettySession session = deviceSessionManager.getSessionByResourceid(resourceid);
        if (session != null && session.isOpen()) {
            try {
                // 如果消息对象中的 timestamp 为空，则按规范由平台填充
                if (messageObject.getTimestamp() == null) {
                    messageObject.setTimestamp(getCurrentUtcTimestamp());
                }
                String jsonMessage = objectMapper.writeValueAsString(messageObject);
                session.sendText(jsonMessage);
                log.debug("成功向 resourceid={} 发送消息: {}", resourceid, jsonMessage);
                return true;
            } catch (JsonProcessingException e) {
                log.error("向 resourceid={} 发送消息时发生JSON序列化错误: {}", resourceid, messageObject, e);
                return false;
            } catch (Exception e) {
                log.error("向 resourceid={} 发送消息失败: {}", resourceid, messageObject, e);
                return false;
            }
        } else {
            log.warn("尝试向离线或不存在的设备 resourceid={} 发送消息: {}", resourceid, messageObject);
            return false;
        }
    }

    /**
     * 向指定设备发送程序任务设置消息
     *
     * @param resourceid 目标设备的resourceid
     * @param taskOrder 任务令
     * @param programs 程序列表
     * @param messageId 可选的消息ID
     * @return 如果发送成功返回true，否则返回false
     */
    public boolean sendProgramTaskToDevice(String resourceid, String taskOrder,
            java.util.List<ProgramInfo> programs, String messageId) {

        SetProgramTaskData taskData = new SetProgramTaskData();
        taskData.setResourceid(resourceid);
        taskData.setTaskOrder(taskOrder);
        taskData.setPrograms(programs);

        BaseWebSocketMessage<SetProgramTaskData> taskMessage = new BaseWebSocketMessage<>(
                "SET_PROGRAM_TASK",
                null, // Timestamp会由sendMessageToDevice自动填充
                messageId,
                taskData
            );

        log.info("向设备 {} 发送程序任务设置，任务令: {}, 程序数量: {}", resourceid, taskOrder, programs.size());
        return sendMessageToDevice(resourceid, taskMessage);
    }

    /**
     * 获取支持的消息类型列表
     */
    public String[] getSupportedMessageTypes() {
        return messageHandlerManager.getSupportedMessageTypes();
    }

    /**
     * 检查是否支持指定的消息类型
     */
    public boolean isMessageTypeSupported(String messageType) {
        return messageHandlerManager.isMessageTypeSupported(messageType);
    }

    /**
     * 获取消息处理器统计信息
     */
    public Map<String, String> getMessageHandlerStatistics() {
        return messageHandlerManager.getHandlerStatistics();
    }

    // 可以根据需要添加广播等其他方法
}