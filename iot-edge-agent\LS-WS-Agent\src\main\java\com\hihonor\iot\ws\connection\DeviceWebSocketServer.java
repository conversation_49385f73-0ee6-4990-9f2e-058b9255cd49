/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.ws.connection;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hihonor.iot.ws.connection.model.BaseWebSocketMessage;
import com.hihonor.iot.ws.connection.model.ConnectionAckData;
import com.hihonor.iot.ws.connection.model.ProgramTaskResultData;

import cn.twelvet.websocket.netty.annotation.BeforeHandshake;
import cn.twelvet.websocket.netty.annotation.OnBinary;
import cn.twelvet.websocket.netty.annotation.OnClose;
import cn.twelvet.websocket.netty.annotation.OnError;
import cn.twelvet.websocket.netty.annotation.OnEvent;
import cn.twelvet.websocket.netty.annotation.OnMessage;
import cn.twelvet.websocket.netty.annotation.OnOpen;
import cn.twelvet.websocket.netty.annotation.RequestParam;
import cn.twelvet.websocket.netty.annotation.WebSocketEndpoint;
import cn.twelvet.websocket.netty.domain.NettySession;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.timeout.IdleStateEvent;

/**
 * 设备 WebSocket 服务器端点
 * 负责处理设备连接、消息和生命周期事件。
 * 
 * <AUTHOR>
 * @since 2025-06-03
 */
@WebSocketEndpoint(path = "/ws/connect", port = "${netty.websocket.port}")
@Component
public class DeviceWebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(DeviceWebSocketServer.class);
    private static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private DeviceSessionManager deviceSessionManager;

    private final ObjectMapper objectMapper;

    @Autowired
    public DeviceWebSocketServer(DeviceSessionManager deviceSessionManager, ObjectMapper objectMapper) {
        this.deviceSessionManager = deviceSessionManager;
        this.objectMapper = objectMapper;
        // 配置 ObjectMapper，例如日期格式，如果需要全局配置的话
        // objectMapper.setDateFormat(new SimpleDateFormat(TIMESTAMP_FORMAT));
        // objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 北京时间
        log.info("DeviceWebSocketServer: 构造函数被调用。DeviceSessionManager 和 ObjectMapper 已注入。");
    }

    /**
     * 生成当前时间的标准格式字符串 (yyyy-MM-dd HH:mm:ss, 北京时间)。
     */
    private String getCurrentFormattedTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat(TIMESTAMP_FORMAT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(new Date());
    }

    /**
     * 握手前进行设备认证和resourceid检查。
     */
    @BeforeHandshake
    public void beforeHandshake(NettySession session, HttpHeaders headers,
            @RequestParam(name = "resourceid", required = false) String queryResourceid) {
        String resourceid = extractResourceid(headers, queryResourceid);

        if (!StringUtils.hasText(resourceid)) {
            log.warn("握手失败：连接请求中缺少有效的 resourceid (请求头 X-resourceid 或 查询参数 resourceid)。");
            session.close();
            return;
        }

        if (!deviceSessionManager.tryRegisterSession(session, resourceid)) {
            session.close();
            return;
        }
        session.setAttribute("resourceid", resourceid);
        log.info("握手成功，设备认证通过: resourceid={}", resourceid);
    }

    /**
     * 设备连接成功建立。
     */
    @OnOpen
    public void onOpen(NettySession session, HttpHeaders headers) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.error("onOpen错误：无法从session获取resourceid，可能在beforeHandshake阶段未成功设置。关闭连接。");
            session.close();
            return;
        }
        log.info("设备连接成功打开: resourceid={}, 当前会话ID: {}, 当前在线设备数: {}",
                resourceid, session.id(), deviceSessionManager.getOnlineDeviceCount());

        ConnectionAckData ackData = new ConnectionAckData("success", resourceid, "设备连接成功");
        BaseWebSocketMessage<ConnectionAckData> ackMessage = new BaseWebSocketMessage<>(
                "CONNECTION_ACK",
                null, // Timestamp会由sendMessageToDevice自动填充
                null, // messageId 可选
                ackData);
        sendMessageToDevice(resourceid, ackMessage);
    }

    /**
     * 设备连接关闭。
     */
    @OnClose
    public void onClose(NettySession session) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (StringUtils.hasText(resourceid)) {
            deviceSessionManager.unregisterSession(session);
            log.info("设备连接已关闭: resourceid={}", resourceid);
        } else {
            log.info("一个未成功认证的会话已关闭: 会话ID={}", session.id());
            deviceSessionManager.unregisterSession(session);
        }
    }

    /**
     * 设备连接发生错误。
     */
    @OnError
    public void onError(NettySession session, Throwable throwable) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (StringUtils.hasText(resourceid)) {
            log.error("设备连接发生错误: resourceid={}", resourceid, throwable);
            deviceSessionManager.unregisterSession(session);
        } else {
            log.error("一个未成功认证的会话发生错误: 会话ID={}", session.id(), throwable);
            deviceSessionManager.unregisterSession(session);
        }
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * 收到来自设备的文本消息（预期为JSON）。
     */
    @OnMessage
    public void onMessage(NettySession session, String message) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.warn("收到来自未认证会话的消息，已忽略。会话ID: {}", session.id());
            session.close();
            return;
        }

        deviceSessionManager.updateLastMessageTime(resourceid);
        log.debug("收到原始设备消息: resourceid={}, message={}", resourceid, message);

        try {
            // 1. 初步反序列化为 BaseWebSocketMessage 以获取 type 和 data (作为 ObjectNode)
            JavaType baseMessageType = objectMapper.getTypeFactory().constructParametricType(BaseWebSocketMessage.class,
                    ObjectNode.class);
            BaseWebSocketMessage<ObjectNode> baseMessage = objectMapper.readValue(message, baseMessageType);

            // 2. 处理 timestamp (如果设备未提供，平台生成)
            if (baseMessage.getTimestamp() == null || baseMessage.getTimestamp().trim().isEmpty()) {
                baseMessage.setTimestamp(getCurrentFormattedTimestamp());
                log.debug("设备 {} 未提供timestamp，平台已生成: {}", resourceid, baseMessage.getTimestamp());
            }

            String messageType = baseMessage.getType();
            ObjectNode dataNode = baseMessage.getData();

            if ("HEARTBEAT".equalsIgnoreCase(messageType)) {
                log.info("收到来自 resourceid={} 的心跳消息(类型解析)。 Timestamp: {}", resourceid, baseMessage.getTimestamp());
                // 可选：进一步解析 HeartbeatData
                // HeartbeatData heartbeatData = null;
                // if (dataNode != null && !dataNode.isNull()) {
                // heartbeatData = objectMapper.treeToValue(dataNode, HeartbeatData.class);
                // }
                // log.info("心跳数据: {}", heartbeatData);

                BaseWebSocketMessage<Object> heartbeatAck = new BaseWebSocketMessage<>("HEARTBEAT_ACK", null, null,
                        null);
                sendMessageToDevice(resourceid, heartbeatAck);
                return;
            }

            // 对于其他消息类型，进一步处理 dataNode
            if (dataNode == null) {
                log.warn("设备 {} 发送的消息类型 {}，但 data 字段为 null。消息: {}", resourceid, messageType, message);
                // 根据业务决定是否需要回复错误，或直接忽略
                return;
            }

            // 3. 根据 messageType 将 dataNode 反序列化为具体 DTO
            // (这里只处理文档中定义的 PROGRAM_TASK_RESULT，其他类型可以后续添加)
            if ("PROGRAM_TASK_RESULT".equalsIgnoreCase(messageType)) {
                ProgramTaskResultData programTaskResultData = objectMapper.treeToValue(dataNode,
                        ProgramTaskResultData.class);
                // 将原始的BaseMessage中的通用字段（如 messageId, 已处理的timestamp）传递给包含具体类型Data的BaseMessage
                BaseWebSocketMessage<ProgramTaskResultData> typedMessage = new BaseWebSocketMessage<>(
                        baseMessage.getType(),
                        baseMessage.getTimestamp(),
                        baseMessage.getMessageId(),
                        programTaskResultData);
                handleDeviceTypedMessage(resourceid, typedMessage);
            } else {
                log.warn("收到来自 resourceid={} 的未知或未处理的消息类型: {}。消息: {}", resourceid, messageType, message);
                // 可选择回复一个通用错误消息或业务错误消息
            }

        } catch (JsonProcessingException e) {
            log.error("反序列化设备消息失败: resourceid={}, message={}", resourceid, message, e);
            // 可选：向设备发送错误提示
        } catch (Exception e) {
            log.error("处理设备消息时发生意外错误: resourceid={}, message={}", resourceid, message, e);
        }
    }

    /**
     * 处理设备发送的、已经类型化的JSON消息的私有方法。
     * 
     * @param resourceid   设备ID
     * @param typedMessage 包含具体Data类型的BaseWebSocketMessage对象
     */
    private void handleDeviceTypedMessage(String resourceid, BaseWebSocketMessage<ProgramTaskResultData> typedMessage) {
        log.info("正在处理来自 resourceid={} 的类型化消息: {}", resourceid, typedMessage);
        // 示例：简单回复一个通用ACK
        BaseWebSocketMessage<Object> messageAck = new BaseWebSocketMessage<>(
                typedMessage.getType() + "_ACK", // e.g., PROGRAM_TASK_RESULT_ACK
                null, // Timestamp会由sendMessageToDevice自动填充
                typedMessage.getMessageId(), // 回传原始messageId
                Map.of("status", "received") // 简单示例data
        );
        sendMessageToDevice(resourceid, messageAck);

        // 具体的业务逻辑可以放在这里，例如：
        // if (typedMessage.getData() instanceof ProgramTaskResultData) {
        // ProgramTaskResultData resultData = typedMessage.getData();
        // // ... 处理 resultData ...
        // }
    }

    /**
     * 收到来自设备的二进制消息。
     */
    @OnBinary
    public void onBinary(NettySession session, byte[] bytes) {
        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            log.warn("收到来自未认证会话的二进制消息，已忽略。会话ID: {}", session.id());
            session.close();
            return;
        }
        deviceSessionManager.updateLastMessageTime(resourceid);
        log.debug("收到设备二进制数据: resourceid={}, length={}", resourceid, bytes.length);
    }

    /**
     * 处理Netty事件，主要用于心跳超时检测。
     */
    @OnEvent
    public void onEvent(NettySession session, Object evt) {
        // 保留此日志以捕获所有到达的事件，但不再特别处理IdleStateEvent.READER_IDLE_STATE_EVENT
        log.info("DeviceWebSocketServer @OnEvent: 收到事件类型: {}, 会话ID: {}", evt.getClass().getName(), session.id());

        String resourceid = (String) session.getAttribute("resourceid");
        if (resourceid == null) {
            // 对于未成功认证的会话，如果触发了IdleStateEvent，仍然可以记录（但不会处理超时）
            if (evt instanceof IdleStateEvent) {
                log.info("未认证会话 {} 触发IdleStateEvent: {}", session.id(), ((IdleStateEvent) evt).state());
            }
            return;
        }

        // 移除了原先处理 IdleStateEvent.READER_IDLE_STATE_EVENT 的逻辑，
        // 因为超时管理现在由 ConnectionTimeoutTask 负责。
        // 可以根据需要在这里处理其他类型的Netty事件。
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent idleEvent = (IdleStateEvent) evt;
            // 记录其他可能的空闲事件，但不做处理
            if (idleEvent.state() != IdleStateEvent.READER_IDLE_STATE_EVENT.state()) {
                log.debug("设备 resourceid={} 触发其他空闲事件: {}", resourceid, idleEvent.state());
            }
        }
    }

    /**
     * 从HTTP头中提取resourceid。
     * 根据最新规范，resourceid必须通过 "X-resourceid" 请求头传递。
     */
    private String extractResourceid(HttpHeaders headers,
            @RequestParam(name = "resourceid", required = false) String queryResourceid) {
        // 根据最新规范，queryResourceid 参数不再使用，但注解保留以避免潜在的框架方法签名匹配问题。
        // 实际逻辑只检查请求头。
        String headerResourceid = headers.get("X-resourceid");
        if (StringUtils.hasText(headerResourceid)) {
            return headerResourceid;
        }
        // 如果请求头中没有 X-resourceid，则返回 null，这将导致握手失败。
        return null;
    }

    /**
     * 向指定设备发送经过包装的 WebSocket 消息对象。
     *
     * @param resourceid    目标设备的resourceid
     * @param messageObject 要发送的 BaseWebSocketMessage 对象
     * @return 如果发送成功返回true，否则返回false
     */
    public boolean sendMessageToDevice(String resourceid, BaseWebSocketMessage<?> messageObject) {
        NettySession session = deviceSessionManager.getSessionByResourceid(resourceid);
        if (session != null && session.isOpen()) {
            try {
                // 如果消息对象中的 timestamp 为空，则按规范由平台填充
                if (messageObject.getTimestamp() == null) {
                    messageObject.setTimestamp(getCurrentFormattedTimestamp());
                }
                String jsonMessage = objectMapper.writeValueAsString(messageObject);
                session.sendText(jsonMessage);
                log.debug("成功向 resourceid={} 发送消息: {}", resourceid, jsonMessage);
                return true;
            } catch (JsonProcessingException e) {
                log.error("向 resourceid={} 发送消息时发生JSON序列化错误: {}", resourceid, messageObject, e);
                return false;
            } catch (Exception e) {
                log.error("向 resourceid={} 发送消息失败: {}", resourceid, messageObject, e);
                return false;
            }
        } else {
            log.warn("尝试向离线或不存在的设备 resourceid={} 发送消息: {}", resourceid, messageObject);
            return false;
        }
    }

    // 可以根据需要添加广播等其他方法
}