<?xml version="1.0" encoding="UTF-8"?><!--
  ~ Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iot-edge-agent</artifactId>
        <groupId>com.hihonor.iot</groupId>
        <version>1.0.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ls-ws-agent</artifactId>

    <dependencies>
        <!--SpringBoot 的aop 模块-->

        <dependency>
            <groupId>com.hihonor.iot</groupId>
            <artifactId>iot-edge-common</artifactId>
            <version>1.1.5</version>
        </dependency>

        <dependency>
            <groupId>com.hihonor.it</groupId>
            <artifactId>apimall-sdk</artifactId>
            <version>1.2.4.200-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.twelvet</groupId>
            <artifactId>netty-websocket-spring-boot-starter</artifactId>
            <version>1.0.1</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-actuator</artifactId>-->
        <!--        </dependency>-->


<!--        <dependency>-->
<!--            <groupId>de.codecentric</groupId>-->
<!--            <artifactId>spring-boot-admin-starter-server</artifactId>-->
<!--            <version>2.7.4</version>-->
<!--        </dependency>-->

        <!-- 移除这个依赖，避免与 netty-websocket-spring-boot-starter 冲突 -->
        <!--
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.68.Final</version>
        </dependency>
        -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>2.7.9</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.curator</groupId>-->
        <!--            <artifactId>curator-framework</artifactId>-->
        <!--            <version>5.5.0</version> &lt;!&ndash; 或者其他的版本 &ndash;&gt;-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>5.5.0</version>  <!-- 请根据需要更改版本 -->
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>org.apache.httpcomponents</groupId>-->
        <!--            <artifactId>httpcore</artifactId>-->
        <!--            <version>4.4.11</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.httpcomponents</groupId>-->
        <!--            <artifactId>httpmime</artifactId>-->
        <!--            <version>4.5.9</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>fastjson</artifactId>-->
        <!--            <version>1.2.80</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/club.javafamily/javafamily-resttemplate-starter -->
        <dependency>
            <groupId>club.javafamily</groupId>
            <artifactId>javafamily-resttemplate-starter</artifactId>
            <version>2.3.2-beta.4</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql-version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid-version}</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>fastjson</artifactId>-->
        <!--            <version>${fastjson-version}</version>-->
        <!--        </dependency>-->

    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <!-- 默认激活 -->

            </activation>
            <properties>
                <!-- 开发 -->
                <profile.environment>dev</profile.environment>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 测试 -->
                <activeByDefault>true</activeByDefault>
                <profile.environment>test</profile.environment>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产 -->
                <profile.environment>prod</profile.environment>
            </properties>
        </profile>
    </profiles>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-${profile.environment}.yml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>bootstrap*.yml</exclude>
                </excludes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--            </plugin>-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <!-- 覆盖原有文件 -->
                            <overwrite>true</overwrite>
                            <outputDirectory>release</outputDirectory>
                            <!-- 待处理的资源定义 -->
                            <resources>
                                <resource>
                                    <!-- 指定resources插件处理哪个目录下的资源文件 -->
                                    <directory>..</directory>
                                    <includes>
                                        <include>linux/*</include>
                                    </includes>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!--                                <move file="${project.build.outputDirectory}/bootstrap-${profile.environment}.yml"-->
                                <!--                                    tofile="${project.build.outputDirectory}/bootstrap.yml" />-->
                                <!--                                <move file="${project.build.outputDirectory}/logback-spring.xml"-->
                                <!--                                    tofile="${project.basedir}/release/logback-spring.xml" />-->
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.hihonor.iot.ws.ls.LsApp</mainClass>
                    <outputDirectory>release</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>4.5.3.0</version>
                <configuration>
                    <failOnError>false</failOnError>
                    <effort>Default</effort>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
