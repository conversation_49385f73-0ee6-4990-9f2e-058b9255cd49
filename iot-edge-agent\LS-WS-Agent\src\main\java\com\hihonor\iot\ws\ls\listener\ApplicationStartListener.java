/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.ws.ls.listener;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import lombok.NoArgsConstructor;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@NoArgsConstructor
@Component
public class ApplicationStartListener implements ApplicationListener<ApplicationEvent> {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartListener.class);
    // @Autowired
    // IotClientStatusListener iotClientStatusListener;
    //
    // @Autowired
    // ConnectedThingClient client;

    /**
     * 事件监听
     *
     * @param event 事件
     */
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        // SpringBoot应用启动且未作任何处理（除listener注册和初始化）的时候发送ApplicationStartingEvent

        if (event instanceof ApplicationStartingEvent) {
            logger.debug("ApplicationStarting...");
            return;
        }

        // 确定springboot应用使用的Environment且context创建之前发送这个事件
        if (event instanceof ApplicationEnvironmentPreparedEvent) {
            logger.debug("ApplicationEnvironmentPrepared...");
            return;
        }

        // context已经创建且没有refresh发送个事件
        if (event instanceof ApplicationPreparedEvent) {
            logger.debug("ApplicationPrepared...");
            return;
        }

        // context已经refresh且application and command-line runners（如果有） 调用之前发送这个事件
        if (event instanceof ApplicationStartedEvent) {
            logger.debug("ApplicationStarted...");
            return;
        }

        if (event instanceof ApplicationReadyEvent) {
            // ApplicationContext context = ((ApplicationReadyEvent)
            // event).getApplicationContext();
            // EdgeManager edgeManager = context.getBean(EdgeManager.class);
            // edgeManager.start();

            return;
        }

        // 应用启动失败后产生这个事件
        if (event instanceof ApplicationFailedEvent) {
            logger.debug("ApplicationFailed...");
            return;
        }
    }
}
