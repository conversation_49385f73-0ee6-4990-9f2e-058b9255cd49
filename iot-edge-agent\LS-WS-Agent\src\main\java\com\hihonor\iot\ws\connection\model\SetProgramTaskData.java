package com.hihonor.iot.ws.connection.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SET_PROGRAM_TASK 消息的 data 字段结构。
 * IOT平台向设备下发程序名称、生产任务令等加工信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetProgramTaskData {

    /**
     * 目标设备的 resourceid (设备资产编码)。
     * (String, Mandatory)
     */
    private String resourceid;

    /**
     * 生产任务令。
     * (String, Mandatory, e.g., "RTZ6H55K16V")
     */
    private String taskOrder;

    /**
     * 程序列表，包含一个或两个轨道的程序信息。
     * (List of ProgramInfo, Mandatory)
     */
    private List<ProgramInfo> programs;
}