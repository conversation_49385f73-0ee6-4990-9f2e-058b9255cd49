/*
 * Copyright (c) 2025, Honor Device Co., Ltd. All rights reserved.
 */

package com.hihonor.iot.ws.connection;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.twelvet.websocket.netty.domain.NettySession;

/**
 * 设备会话管理器
 * 负责管理活动的设备连接、resourceid的唯一性及设备信息。
 * 
 * <AUTHOR>
 * @since 2025-06-03
 */
@Component
public class DeviceSessionManager {
    private static final Logger log = LoggerFactory.getLogger(DeviceSessionManager.class);

    private final Map<String, NettySession> sessionsByResourceid = new ConcurrentHashMap<>();
    private final Map<NettySession, String> resourceidBySession = new ConcurrentHashMap<>();
    private final Map<String, DeviceInfo> deviceInfoMapByResourceid = new ConcurrentHashMap<>();

    /**
     * 尝试注册新的设备会话。
     *
     * @param session    Netty会话
     * @param resourceid 设备唯一标识
     * @return 如果注册成功返回true，如果resourceid已存在或无效则返回false
     */
    public synchronized boolean tryRegisterSession(NettySession session, String resourceid) {
        if (resourceid == null || resourceid.trim().isEmpty()) {
            log.warn("尝试注册会话失败：resourceid为空或无效。");
            return false;
        }

        if (sessionsByResourceid.containsKey(resourceid)) {
            log.warn("尝试注册会话失败：resourceid '{}' 的连接已存在。拒绝新的连接请求。", resourceid);
            return false;
        }

        sessionsByResourceid.put(resourceid, session);
        resourceidBySession.put(session, resourceid);
        DeviceInfo deviceInfo = new DeviceInfo(resourceid);
        deviceInfoMapByResourceid.put(resourceid, deviceInfo);
        log.info("设备会话注册成功: resourceid={}, 当前在线设备数: {}", resourceid, sessionsByResourceid.size());
        return true;
    }

    /**
     * 注销设备会话。
     *
     * @param session Netty会话
     */
    public synchronized void unregisterSession(NettySession session) {
        String resourceid = resourceidBySession.remove(session);
        if (resourceid != null) {
            sessionsByResourceid.remove(resourceid);
            deviceInfoMapByResourceid.remove(resourceid);
            log.info("设备会话已注销: resourceid={}, 当前在线设备数: {}", resourceid, sessionsByResourceid.size());
        } else {
            log.warn("尝试注销一个未记录resourceid的会话。");
        }
    }

    /**
     * 根据resourceid获取NettySession。
     *
     * @param resourceid 设备唯一标识
     * @return NettySession，如果不存在则返回null
     */
    public NettySession getSessionByResourceid(String resourceid) {
        return sessionsByResourceid.get(resourceid);
    }

    /**
     * 根据NettySession获取resourceid。
     *
     * @param session Netty会话
     * @return resourceid，如果不存在则返回null
     */
    public String getResourceidBySession(NettySession session) {
        return resourceidBySession.get(session);
    }

    /**
     * 更新指定设备的最后消息时间。
     *
     * @param resourceid 设备唯一标识
     */
    public void updateLastMessageTime(String resourceid) {
        DeviceInfo deviceInfo = deviceInfoMapByResourceid.get(resourceid);
        if (deviceInfo != null) {
            deviceInfo.setLastMessageTime(System.currentTimeMillis());
        } else {
            log.warn("尝试更新一个不存在的设备信息: resourceid={}", resourceid);
        }
    }

    /**
     * 获取设备信息。
     *
     * @param resourceid 设备唯一标识
     * @return DeviceInfo对象，如果不存在则返回null
     */
    public DeviceInfo getDeviceInfo(String resourceid) {
        return deviceInfoMapByResourceid.get(resourceid);
    }

    /**
     * 获取当前在线设备数量。
     *
     * @return 在线设备数量
     */
    public int getOnlineDeviceCount() {
        return sessionsByResourceid.size();
    }

    /**
     * 获取所有在线设备的resourceid集合。
     *
     * @return resourceid集合
     */
    public Set<String> getAllOnlineResourceids() {
        return sessionsByResourceid.keySet();
    }

    /**
     * 获取所有在线设备的设备信息映射。
     * 主要供定时任务检查连接超时使用。
     *
     * @return 一个包含所有 resourceid 到 DeviceInfo 的映射的不可变副本。
     */
    public Map<String, DeviceInfo> getAllDeviceInfos() {
        // 返回一个副本以防止并发修改问题，尽管当前 deviceInfoMapByResourceid 是 ConcurrentHashMap
        // 但外部迭代时仍建议使用快照或同步块。这里直接返回引用，依赖调用者正确处理迭代。
        // 或者更安全的做法是返回一个不可修改的Map的副本:
        // return Map.copyOf(deviceInfoMapByResourceid);
        // 但为了简单起见，并且假设定时任务的迭代是线程安全的（例如，它不修改map），先直接返回引用。
        // 如果定时任务需要删除条目，应通过 DeviceSessionManager 的方法进行，而不是直接操作返回的Map。
        return deviceInfoMapByResourceid;
    }
}